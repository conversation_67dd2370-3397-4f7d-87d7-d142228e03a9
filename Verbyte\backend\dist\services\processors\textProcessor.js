"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class TextProcessor {
    supports(mimeType) {
        return mimeType === 'text/plain' ||
            mimeType.startsWith('text/') ||
            mimeType === 'application/octet-stream';
    }
    getType() {
        return document_1.DocumentType.TEXT;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing text file: ${filePath}`);
            const { text, encoding } = await this.readTextFile(filePath);
            const metadata = await this.extractMetadata(filePath, text, encoding);
            const chunks = options?.chunkSize ?
                this.createChunks(text, options.chunkSize, options.chunkOverlap || 0) :
                undefined;
            metadata.wordCount = this.countWords(text);
            metadata.characterCount = text.length;
            metadata.encoding = encoding;
            const result = {
                text,
                metadata,
                chunks,
                processingTime: Date.now() - startTime,
                success: true
            };
            logger_1.logger.debug(`Text processing completed: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown text processing error';
            logger_1.logger.error(`Text processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    async readTextFile(filePath) {
        try {
            const text = fs_1.default.readFileSync(filePath, 'utf-8');
            if (text.includes('\uFFFD')) {
                const encodings = ['latin1', 'ascii'];
                for (const encoding of encodings) {
                    try {
                        const alternativeText = fs_1.default.readFileSync(filePath, encoding);
                        if (!alternativeText.includes('\uFFFD')) {
                            return { text: alternativeText, encoding };
                        }
                    }
                    catch (error) {
                    }
                }
            }
            return { text, encoding: 'utf-8' };
        }
        catch (error) {
            throw new Error(`Failed to read text file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async extractMetadata(filePath, text, encoding) {
        try {
            const stats = fs_1.default.statSync(filePath);
            const lineCount = text.split('\n').length;
            const language = this.detectLanguage(text);
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: 'text/plain',
                size: stats.size,
                type: document_1.DocumentType.TEXT,
                encoding,
                language,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime,
                lines: lineCount
            };
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract text metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    detectLanguage(text) {
        const sample = text.toLowerCase().substring(0, 1000);
        const languagePatterns = {
            'en': ['the', 'and', 'that', 'have', 'for', 'not', 'with', 'you', 'this', 'but'],
            'es': ['que', 'de', 'no', 'la', 'el', 'en', 'y', 'a', 'es', 'se'],
            'fr': ['que', 'de', 'et', 'le', 'la', 'les', 'des', 'en', 'un', 'du'],
            'de': ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich'],
            'it': ['che', 'di', 'la', 'il', 'e', 'le', 'un', 'in', 'per', 'non']
        };
        let bestLanguage = 'unknown';
        let maxMatches = 0;
        Object.entries(languagePatterns).forEach(([lang, words]) => {
            const matches = words.filter(word => sample.includes(` ${word} `) ||
                sample.startsWith(`${word} `) ||
                sample.endsWith(` ${word}`)).length;
            if (matches > maxMatches) {
                maxMatches = matches;
                bestLanguage = lang;
            }
        });
        return maxMatches > 2 ? bestLanguage : 'unknown';
    }
    createChunks(text, chunkSize, overlap) {
        const chunks = [];
        const paragraphs = text.split(/\n\s*\n/);
        if (paragraphs.length > 1 && paragraphs.some(p => p.length < chunkSize)) {
            let currentChunk = '';
            let currentStart = 0;
            paragraphs.forEach((paragraph, index) => {
                const trimmedParagraph = paragraph.trim();
                if (currentChunk.length + trimmedParagraph.length > chunkSize && currentChunk.length > 0) {
                    chunks.push({
                        id: `text_paragraph_chunk_${chunks.length}`,
                        text: currentChunk.trim(),
                        startIndex: currentStart,
                        endIndex: currentStart + currentChunk.length,
                        metadata: {
                            source: 'text',
                            chunkIndex: chunks.length,
                            type: 'paragraph'
                        }
                    });
                    const overlapText = currentChunk.substring(Math.max(0, currentChunk.length - overlap));
                    currentChunk = overlapText + '\n\n' + trimmedParagraph;
                    currentStart += currentChunk.length - overlapText.length - trimmedParagraph.length - 2;
                }
                else {
                    if (currentChunk.length > 0) {
                        currentChunk += '\n\n';
                    }
                    currentChunk += trimmedParagraph;
                }
                if (index === paragraphs.length - 1 && currentChunk.trim().length > 0) {
                    chunks.push({
                        id: `text_paragraph_chunk_${chunks.length}`,
                        text: currentChunk.trim(),
                        startIndex: currentStart,
                        endIndex: currentStart + currentChunk.length,
                        metadata: {
                            source: 'text',
                            chunkIndex: chunks.length,
                            type: 'paragraph'
                        }
                    });
                }
            });
        }
        else {
            let currentPos = 0;
            while (currentPos < text.length) {
                const endPos = Math.min(currentPos + chunkSize, text.length);
                const chunkText = text.substring(currentPos, endPos);
                chunks.push({
                    id: `text_chunk_${chunks.length}`,
                    text: chunkText,
                    startIndex: currentPos,
                    endIndex: endPos,
                    metadata: {
                        source: 'text',
                        chunkIndex: chunks.length,
                        type: 'character'
                    }
                });
                currentPos = endPos - overlap;
                if (currentPos >= text.length)
                    break;
            }
        }
        return chunks;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'text/plain',
            size: stats.size,
            type: document_1.DocumentType.TEXT,
            encoding: 'utf-8',
            language: 'unknown',
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.TextProcessor = TextProcessor;
//# sourceMappingURL=textProcessor.js.map