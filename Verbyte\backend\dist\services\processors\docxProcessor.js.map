{"version": 3, "file": "docxProcessor.js", "sourceRoot": "", "sources": ["../../../src/services/processors/docxProcessor.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,sDAA8B;AAC9B,2CAAwC;AACxC,+CAO0B;AAE1B,MAAa,aAAa;IAIxB,QAAQ,CAAC,QAAgB;QACvB,OAAO,QAAQ,KAAK,yEAAyE;YACtF,QAAQ,KAAK,oBAAoB;YACjC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YACzB,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAKD,OAAO;QACL,OAAO,uBAAY,CAAC,IAAI,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,QAAgB,EAAE,OAA2B;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;YAGlD,MAAM,MAAM,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAGzC,MAAM,cAAc,GAAG;gBACrB,YAAY,EAAE,iBAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,KAAU,EAAE,EAAE;oBAErD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,WAAgB,EAAE,EAAE;wBACpD,OAAO;4BACL,GAAG,EAAE,QAAQ,KAAK,CAAC,WAAW,WAAW,WAAW,EAAE;yBACvD,CAAC;oBACJ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;gBACF,sBAAsB,EAAE,IAAI;gBAC5B,uBAAuB,EAAE,IAAI;aAC9B,CAAC;YAGF,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACjD,iBAAO,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;gBAClC,OAAO,EAAE,kBAAkB,CAAC,CAAC,CAAC,iBAAO,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;aACxG,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;YAG3F,MAAM,MAAM,GAAG,OAAO,EAAE,SAAS,CAAC,CAAC;gBACjC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;gBACnF,SAAS,CAAC;YAGZ,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACvD,QAAQ,CAAC,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YAElD,MAAM,MAAM,GAAyB;gBACnC,IAAI,EAAE,UAAU,CAAC,KAAK;gBACtB,QAAQ;gBACR,MAAM;gBACN,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,IAAI;aACd,CAAC;YAGF,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnC,eAAM,CAAC,IAAI,CAAC,gCAAgC,QAAQ,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;YAChF,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,8BAA8B,QAAQ,KAAK,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;YACpF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,+BAA+B,CAAC;YAC9F,eAAM,CAAC,KAAK,CAAC,2BAA2B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAE3D,OAAO;gBACL,IAAI,EAAE,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC;gBAC1D,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,IAAY,EAAE,IAAa;QACzE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAGpC,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACvD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAE3E,MAAM,QAAQ,GAAiB;gBAC7B,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACrC,QAAQ,EAAE,yEAAyE;gBACnF,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,uBAAY,CAAC,IAAI;gBACvB,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,SAAS;gBACT,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,UAAU,EAAE,KAAK,CAAC,KAAK;aACxB,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAKO,YAAY,CAAC,IAAY,EAAE,SAAiB,EAAE,OAAe;QACnE,MAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAErD,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,cAAc,MAAM,CAAC,MAAM,EAAE;gBACjC,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,UAAU;gBACtB,QAAQ,EAAE,MAAM;gBAChB,QAAQ,EAAE;oBACR,MAAM,EAAE,MAAM;oBACd,UAAU,EAAE,MAAM,CAAC,MAAM;iBAC1B;aACF,CAAC,CAAC;YAGH,UAAU,GAAG,MAAM,GAAG,OAAO,CAAC;YAC9B,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM;gBAAE,MAAM;QACvC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,UAAU,CAAC,IAAY;QAE7B,MAAM,cAAc,GAAG;YACrB,qBAAqB;YACrB,iBAAiB;YACjB,kBAAkB;YAClB,kBAAkB;YAClB,8BAA8B;SAC/B,CAAC;QAEF,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAKO,UAAU,CAAC,IAAY;QAE7B,MAAM,cAAc,GAAG;YACrB,aAAa;YACb,UAAU;YACV,WAAW;YACX,YAAY;SACb,CAAC;QAEF,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAKO,YAAY,CAAC,IAAY;QAE/B,MAAM,aAAa,GAAG;YACpB,YAAY;YACZ,YAAY;YACZ,sBAAsB;SACvB,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3D,CAAC;IAKO,WAAW,CAAC,IAAY;QAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAClD,OAAO,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAKO,UAAU,CAAC,IAAY;QAC7B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IACzE,CAAC;IAKO,mBAAmB,CAAC,QAAgB;QAC1C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,OAAO;YACL,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrC,QAAQ,EAAE,yEAAyE;YACnF,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,uBAAY,CAAC,IAAI;YACvB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,KAAK;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,KAAK,CAAC,KAAK;SACxB,CAAC;IACJ,CAAC;IAKO,mBAAmB,CAAC,QAAgB,EAAE,KAAa;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO;YACL,GAAG,aAAa;YAChB,KAAK;SACN,CAAC;IACJ,CAAC;CACF;AAzPD,sCAyPC"}