{"version": 3, "file": "textProcessor.js", "sourceRoot": "", "sources": ["../../../src/services/processors/textProcessor.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,2CAAwC;AACxC,+CAO0B;AAE1B,MAAa,aAAa;IAIxB,QAAQ,CAAC,QAAgB;QACvB,OAAO,QAAQ,KAAK,YAAY;YACzB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;YAC5B,QAAQ,KAAK,0BAA0B,CAAC;IACjD,CAAC;IAKD,OAAO;QACL,OAAO,uBAAY,CAAC,IAAI,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,QAAgB,EAAE,OAA2B;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;YAGlD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAG7D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAGtE,MAAM,MAAM,GAAG,OAAO,EAAE,SAAS,CAAC,CAAC;gBACjC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvE,SAAS,CAAC;YAGZ,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3C,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;YACtC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE7B,MAAM,MAAM,GAAyB;gBACnC,IAAI;gBACJ,QAAQ;gBACR,MAAM;gBACN,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,IAAI;aACd,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,8BAA8B,QAAQ,KAAK,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;YACpF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,+BAA+B,CAAC;YAC9F,eAAM,CAAC,KAAK,CAAC,2BAA2B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAE3D,OAAO;gBACL,IAAI,EAAE,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC;gBAC1D,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,YAAY,CAAC,QAAgB;QACzC,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAGhD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAE5B,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAEtC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBACjC,IAAI,CAAC;wBACH,MAAM,eAAe,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,QAA0B,CAAC,CAAC;wBAC9E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;4BACxC,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,CAAC;wBAC7C,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;oBAEjB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC3G,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,IAAY,EAAE,QAAgB;QAC5E,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAGpC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE3C,MAAM,QAAQ,GAAqB;gBACjC,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACrC,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,uBAAY,CAAC,IAAI;gBACvB,QAAQ;gBACR,QAAQ;gBACR,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,UAAU,EAAE,KAAK,CAAC,KAAK;gBAEvB,KAAK,EAAE,SAAS;aACjB,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAKO,cAAc,CAAC,IAAY;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAErD,MAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;YAChF,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;YACjE,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACrE,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;YAC3E,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;SACrE,CAAC;QAEF,IAAI,YAAY,GAAG,SAAS,CAAC;QAC7B,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YACzD,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAClC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,GAAG,CAAC;gBAC5B,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,CAAC;gBAC7B,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAC5B,CAAC,MAAM,CAAC;YAET,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;gBACzB,UAAU,GAAG,OAAO,CAAC;gBACrB,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC;IACnD,CAAC;IAKO,YAAY,CAAC,IAAY,EAAE,SAAiB,EAAE,OAAe;QACnE,MAAM,MAAM,GAAgB,EAAE,CAAC;QAG/B,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAEzC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC;YAExE,IAAI,YAAY,GAAG,EAAE,CAAC;YACtB,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;gBACtC,MAAM,gBAAgB,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;gBAE1C,IAAI,YAAY,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,GAAG,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAEzF,MAAM,CAAC,IAAI,CAAC;wBACV,EAAE,EAAE,wBAAwB,MAAM,CAAC,MAAM,EAAE;wBAC3C,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE;wBACzB,UAAU,EAAE,YAAY;wBACxB,QAAQ,EAAE,YAAY,GAAG,YAAY,CAAC,MAAM;wBAC5C,QAAQ,EAAE;4BACR,MAAM,EAAE,MAAM;4BACd,UAAU,EAAE,MAAM,CAAC,MAAM;4BACzB,IAAI,EAAE,WAAW;yBAClB;qBACF,CAAC,CAAC;oBAGH,MAAM,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC;oBACvF,YAAY,GAAG,WAAW,GAAG,MAAM,GAAG,gBAAgB,CAAC;oBACvD,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;gBACzF,CAAC;qBAAM,CAAC;oBACN,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,YAAY,IAAI,MAAM,CAAC;oBACzB,CAAC;oBACD,YAAY,IAAI,gBAAgB,CAAC;gBACnC,CAAC;gBAGD,IAAI,KAAK,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtE,MAAM,CAAC,IAAI,CAAC;wBACV,EAAE,EAAE,wBAAwB,MAAM,CAAC,MAAM,EAAE;wBAC3C,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE;wBACzB,UAAU,EAAE,YAAY;wBACxB,QAAQ,EAAE,YAAY,GAAG,YAAY,CAAC,MAAM;wBAC5C,QAAQ,EAAE;4BACR,MAAM,EAAE,MAAM;4BACd,UAAU,EAAE,MAAM,CAAC,MAAM;4BACzB,IAAI,EAAE,WAAW;yBAClB;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YAEN,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAErD,MAAM,CAAC,IAAI,CAAC;oBACV,EAAE,EAAE,cAAc,MAAM,CAAC,MAAM,EAAE;oBACjC,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,UAAU;oBACtB,QAAQ,EAAE,MAAM;oBAChB,QAAQ,EAAE;wBACR,MAAM,EAAE,MAAM;wBACd,UAAU,EAAE,MAAM,CAAC,MAAM;wBACzB,IAAI,EAAE,WAAW;qBAClB;iBACF,CAAC,CAAC;gBAGH,UAAU,GAAG,MAAM,GAAG,OAAO,CAAC;gBAC9B,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM;oBAAE,MAAM;YACvC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,UAAU,CAAC,IAAY;QAC7B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IACzE,CAAC;IAKO,mBAAmB,CAAC,QAAgB;QAC1C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,OAAO;YACL,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,uBAAY,CAAC,IAAI;YACvB,QAAQ,EAAE,OAAO;YACjB,QAAQ,EAAE,SAAS;YACnB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,KAAK,CAAC,KAAK;SACxB,CAAC;IACJ,CAAC;IAKO,mBAAmB,CAAC,QAAgB,EAAE,KAAa;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO;YACL,GAAG,aAAa;YAChB,KAAK;SACN,CAAC;IACJ,CAAC;CACF;AA7RD,sCA6RC"}