"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegratedDocumentProcessingService = void 0;
const logger_1 = require("@/utils/logger");
const uploadService_1 = require("./uploadService");
const documentProcessor_1 = require("./documentProcessor");
const Upload_1 = require("@/models/Upload");
class IntegratedDocumentProcessingService {
    constructor() {
        this.uploadService = new uploadService_1.UploadService();
        this.documentProcessor = new documentProcessor_1.DocumentProcessingService();
    }
    async processUploadedDocument(uploadId, options) {
        try {
            logger_1.logger.info(`Starting document processing for upload: ${uploadId}`);
            const upload = await this.uploadService.getUploadById(uploadId);
            if (!upload) {
                throw new Error(`Upload not found: ${uploadId}`);
            }
            await this.uploadService.updateStatus(uploadId, Upload_1.UploadStatus.PROCESSING, Upload_1.ProcessingStatus.EXTRACTING_TEXT);
            await this.uploadService.updateProgress(uploadId, 100, 10);
            const result = await this.documentProcessor.processDocument(upload.path, options);
            if (result.success) {
                await this.updateUploadMetadata(upload, result);
                await this.uploadService.updateStatus(uploadId, Upload_1.UploadStatus.COMPLETED, Upload_1.ProcessingStatus.COMPLETED);
                await this.uploadService.updateProgress(uploadId, 100, 100);
                logger_1.logger.info(`Document processing completed successfully for upload: ${uploadId}`);
            }
            else {
                await this.uploadService.setError(uploadId, result.error || 'Document processing failed', 'PROCESSING_FAILED');
                logger_1.logger.error(`Document processing failed for upload: ${uploadId}`, result.error);
            }
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown processing error';
            await this.uploadService.setError(uploadId, errorMessage, 'PROCESSING_ERROR');
            logger_1.logger.error(`Document processing error for upload: ${uploadId}`, error);
            throw error;
        }
    }
    async processBatchUploads(uploadIds, options) {
        logger_1.logger.info(`Starting batch processing for ${uploadIds.length} uploads`);
        const results = [];
        for (const uploadId of uploadIds) {
            try {
                const result = await this.processUploadedDocument(uploadId, options);
                results.push({ uploadId, result });
            }
            catch (error) {
                logger_1.logger.error(`Failed to process upload ${uploadId} in batch:`, error);
                results.push({
                    uploadId,
                    result: {
                        text: '',
                        metadata: {
                            filename: 'unknown',
                            originalName: 'unknown',
                            mimeType: 'unknown',
                            size: 0,
                            type: 'text',
                            error: error instanceof Error ? error.message : 'Unknown error'
                        },
                        processingTime: 0,
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    }
                });
            }
        }
        logger_1.logger.info(`Batch processing completed: ${results.filter(r => r.result.success).length}/${uploadIds.length} successful`);
        return results;
    }
    async processPendingUploads(options) {
        try {
            const pendingUploads = await this.uploadService.getPendingUploads();
            if (pendingUploads.length === 0) {
                logger_1.logger.debug('No pending uploads to process');
                return 0;
            }
            logger_1.logger.info(`Found ${pendingUploads.length} pending uploads to process`);
            const uploadIds = pendingUploads.map(upload => upload._id.toString());
            await this.processBatchUploads(uploadIds, options);
            return pendingUploads.length;
        }
        catch (error) {
            logger_1.logger.error('Failed to process pending uploads:', error);
            throw error;
        }
    }
    isFileSupported(filePath) {
        return this.documentProcessor.isSupported(filePath);
    }
    getSupportedExtensions() {
        return this.documentProcessor.getSupportedExtensions();
    }
    async updateUploadMetadata(upload, result) {
        try {
            upload.metadata = {
                ...upload.metadata,
                extractedText: true,
                pages: result.metadata.pages,
                wordCount: result.metadata.wordCount,
                characterCount: result.metadata.characterCount,
                language: result.metadata.language,
                documentType: result.metadata.type,
                processingTime: result.processingTime,
                chunks: result.chunks?.length || 0
            };
            await upload.save();
            logger_1.logger.debug(`Updated metadata for upload: ${upload._id}`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to update upload metadata: ${upload._id}`, error);
        }
    }
    async getProcessingStats() {
        try {
            const stats = await this.uploadService.getUploadStats();
            const totalProcessed = stats.byProcessingStatus.completed + stats.byProcessingStatus.failed;
            const successfulProcessing = stats.byProcessingStatus.completed;
            const failedProcessing = stats.byProcessingStatus.failed;
            const pendingProcessing = stats.byProcessingStatus.pending;
            const averageProcessingTime = 0;
            return {
                totalProcessed,
                successfulProcessing,
                failedProcessing,
                pendingProcessing,
                averageProcessingTime
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get processing statistics:', error);
            throw error;
        }
    }
    async reprocessUpload(uploadId, options) {
        try {
            logger_1.logger.info(`Reprocessing upload: ${uploadId}`);
            await this.uploadService.updateStatus(uploadId, Upload_1.UploadStatus.UPLOADED, Upload_1.ProcessingStatus.PENDING);
            await this.uploadService.updateProgress(uploadId, 100, 0);
            return await this.processUploadedDocument(uploadId, options);
        }
        catch (error) {
            logger_1.logger.error(`Failed to reprocess upload: ${uploadId}`, error);
            throw error;
        }
    }
    getProcessorConfig() {
        return this.documentProcessor.getConfig();
    }
    updateProcessorConfig(config) {
        this.documentProcessor.updateConfig(config);
    }
}
exports.IntegratedDocumentProcessingService = IntegratedDocumentProcessingService;
//# sourceMappingURL=documentProcessingService.js.map