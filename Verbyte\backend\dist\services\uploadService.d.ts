import { IUpload, UploadStatus, ProcessingStatus } from '@/models/Upload';
export interface CreateUploadData {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    path: string;
    userId?: string | undefined;
}
export interface UploadProgress {
    uploadId: string;
    uploadProgress: number;
    processingProgress: number;
    status: UploadStatus;
    processingStatus: ProcessingStatus;
    error?: string | undefined;
}
export declare class UploadService {
    createUpload(data: CreateUploadData): Promise<IUpload>;
    getUploadById(uploadId: string): Promise<IUpload | null>;
    getUploadsByUserId(userId: string, limit?: number, offset?: number): Promise<IUpload[]>;
    getAllUploads(limit?: number, offset?: number): Promise<IUpload[]>;
    updateProgress(uploadId: string, uploadProgress?: number, processingProgress?: number): Promise<IUpload>;
    updateStatus(uploadId: string, status: UploadStatus, processingStatus?: ProcessingStatus): Promise<IUpload>;
    setError(uploadId: string, error: string | Error, code?: string): Promise<IUpload>;
    deleteUpload(uploadId: string): Promise<void>;
    getProgress(uploadId: string): Promise<UploadProgress>;
    getPendingUploads(): Promise<IUpload[]>;
    cleanupFailedUploads(olderThanDays?: number): Promise<number>;
    getUploadStats(): Promise<{
        total: number;
        byStatus: Record<UploadStatus, number>;
        byProcessingStatus: Record<ProcessingStatus, number>;
        totalSize: number;
    }>;
}
//# sourceMappingURL=uploadService.d.ts.map