# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Halaman Sebelum
previous_label=Terdahulu
next.title=Halaman Seterusnya
next_label=Berikut

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Halaman
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=daripada {{pageCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} daripada {{pagesCount}})

zoom_out.title=Zum Keluar
zoom_out_label=Zum Keluar
zoom_in.title=Zum Masuk
zoom_in_label=Zum Masuk
zoom.title=Zum
presentation_mode.title=Tukar ke Mod Persembahan
presentation_mode_label=Mod Persembahan
open_file.title=Buka Fail
open_file_label=Buka
print.title=Cetak
print_label=Cetak
download.title=Muat turun
download_label=Muat turun
bookmark.title=Paparan semasa (salin atau buka dalam tetingkap baru)
bookmark_label=Paparan Semasa

# Secondary toolbar and context menu
tools.title=Alatan
tools_label=Alatan
first_page.title=Pergi ke Halaman Pertama
first_page.label=Pergi ke Halaman Pertama
first_page_label=Pergi ke Halaman Pertama
last_page.title=Pergi ke Halaman Terakhir
last_page.label=Pergi ke Halaman Terakhir
last_page_label=Pergi ke Halaman Terakhir
page_rotate_cw.title=Berputar ikut arah Jam
page_rotate_cw.label=Berputar ikut arah Jam
page_rotate_cw_label=Berputar ikut arah Jam
page_rotate_ccw.title=Pusing berlawan arah jam
page_rotate_ccw.label=Pusing berlawan arah jam
page_rotate_ccw_label=Pusing berlawan arah jam

cursor_text_select_tool.title=Dayakan Alatan Pilihan Teks
cursor_text_select_tool_label=Alatan Pilihan Teks
cursor_hand_tool.title=Dayakan Alatan Tangan
cursor_hand_tool_label=Alatan Tangan

# Document properties dialog box
document_properties.title=Sifat Dokumen…
document_properties_label=Sifat Dokumen…
document_properties_file_name=Nama fail:
document_properties_file_size=Saiz fail:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bait)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bait)
document_properties_title=Tajuk:
document_properties_author=Pengarang:
document_properties_subject=Subjek:
document_properties_keywords=Kata kunci:
document_properties_creation_date=Masa Dicipta:
document_properties_modification_date=Tarikh Ubahsuai:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Pencipta:
document_properties_producer=Pengeluar PDF:
document_properties_version=Versi PDF:
document_properties_page_count=Kiraan Laman:
document_properties_close=Tutup

print_progress_message=Menyediakan dokumen untuk dicetak…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Batal

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Togol Bar Sisi
toggle_sidebar_notification.title=Togol Sidebar (dokumen mengandungi rangka/attachments)
toggle_sidebar_label=Togol Bar Sisi
document_outline.title=Papar Rangka Dokumen (klik-dua-kali untuk kembangkan/kolaps semua item)
document_outline_label=Rangka Dokumen
attachments.title=Papar Lampiran
attachments_label=Lampiran
thumbs.title=Papar Thumbnails
thumbs_label=Imej kecil
findbar.title=Cari didalam Dokumen
findbar_label=Cari

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Halaman {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Halaman Imej kecil {{page}}

# Find panel button title and messages
find_input.title=Cari
find_input.placeholder=Cari dalam dokumen…
find_previous.title=Cari teks frasa berkenaan yang terdahulu
find_previous_label=Sebelumnya
find_next.title=Cari teks frasa berkenaan yang berikut
find_next_label=Berikut
find_highlight=Serlahkan semua
find_match_case_label=Kes Sepadan
find_reached_top=Mencapai teratas daripada dokumen, sambungan daripada bawah
find_reached_bottom=Mencapai terakhir daripada dokumen, sambungan daripada atas
find_not_found=Frasa tidak ditemui

# Error panel labels
error_more_info=Maklumat lanjut
error_less_info=Kurang Informasi
error_close=Tutup
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Mesej: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Timbun: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Fail: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Garis: {{line}}
rendering_error=Ralat berlaku ketika memberikan halaman.

# Predefined zoom values
page_scale_width=Lebar Halaman
page_scale_fit=Muat Halaman
page_scale_auto=Zoom Automatik
page_scale_actual=Saiz Sebenar
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Ralat
loading_error=Masalah berlaku semasa menuatkan sebuah PDF.
invalid_file_error=Tidak sah atau fail PDF rosak.
missing_file_error=Fail PDF Hilang.
unexpected_response_error=Respon pelayan yang tidak dijangka.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Anotasi]
password_label=Masukan kata kunci untuk membuka fail PDF ini.
password_invalid=Kata laluan salah. Cuba lagi.
password_ok=OK
password_cancel=Batal

printing_not_supported=Amaran: Cetakan ini tidak sepenuhnya disokong oleh pelayar ini.
printing_not_ready=Amaran: PDF tidak sepenuhnya dimuatkan untuk dicetak.
web_fonts_disabled=Fon web dinyahdayakan: tidak dapat menggunakan fon terbenam PDF.
document_colors_not_allowed=Dokumen PDF tidak dibenarkan untuk menggunakan warna sendiri: “Izinkan halaman untuk memilih warna sendiri” telah dinyahaktifkan dalam pelayar.
