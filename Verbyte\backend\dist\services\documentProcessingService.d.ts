import { TextExtractionResult, ProcessingOptions } from '@/types/document';
export declare class IntegratedDocumentProcessingService {
    private uploadService;
    private documentProcessor;
    constructor();
    processUploadedDocument(uploadId: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    processBatchUploads(uploadIds: string[], options?: ProcessingOptions): Promise<{
        uploadId: string;
        result: TextExtractionResult;
    }[]>;
    processPendingUploads(options?: ProcessingOptions): Promise<number>;
    isFileSupported(filePath: string): boolean;
    getSupportedExtensions(): string[];
    private updateUploadMetadata;
    getProcessingStats(): Promise<{
        totalProcessed: number;
        successfulProcessing: number;
        failedProcessing: number;
        pendingProcessing: number;
        averageProcessingTime: number;
    }>;
    reprocessUpload(uploadId: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    getProcessorConfig(): import("@/types/document").DocumentProcessorConfig;
    updateProcessorConfig(config: any): void;
}
//# sourceMappingURL=documentProcessingService.d.ts.map