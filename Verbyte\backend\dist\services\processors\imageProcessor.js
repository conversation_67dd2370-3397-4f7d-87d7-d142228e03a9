"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const tesseract_js_1 = __importDefault(require("tesseract.js"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class ImageProcessor {
    supports(mimeType) {
        return mimeType.startsWith('image/') ||
            ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff'].includes(mimeType);
    }
    getType() {
        return document_1.DocumentType.IMAGE;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing image file: ${filePath}`);
            if (!fs_1.default.existsSync(filePath)) {
                throw new Error(`Image file not found: ${filePath}`);
            }
            const metadata = await this.extractMetadata(filePath);
            const ocrResult = await this.performOCR(filePath, options?.ocrLanguage || 'eng');
            const text = ocrResult.data.text.trim();
            const confidence = ocrResult.data.confidence;
            metadata.hasText = text.length > 0;
            metadata.confidence = confidence;
            metadata.ocrLanguage = options?.ocrLanguage || 'eng';
            metadata.wordCount = this.countWords(text);
            metadata.characterCount = text.length;
            const chunks = options?.chunkSize && text.length > 0 ?
                this.createChunks(text, options.chunkSize, options.chunkOverlap || 0) :
                undefined;
            const result = {
                text,
                metadata,
                chunks,
                processingTime: Date.now() - startTime,
                success: true
            };
            logger_1.logger.debug(`Image OCR processing completed: ${filePath} (${result.processingTime}ms, confidence: ${confidence}%)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
            logger_1.logger.error(`Image processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    async performOCR(filePath, language) {
        try {
            logger_1.logger.debug(`Starting OCR for ${filePath} with language: ${language}`);
            const result = await tesseract_js_1.default.recognize(filePath, language, {
                logger: (m) => {
                    if (m.status === 'recognizing text') {
                        logger_1.logger.debug(`OCR progress: ${Math.round(m.progress * 100)}%`);
                    }
                }
            });
            return result;
        }
        catch (error) {
            logger_1.logger.error(`OCR failed for ${filePath}:`, error);
            throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async extractMetadata(filePath) {
        try {
            const stats = fs_1.default.statSync(filePath);
            const ext = path_1.default.extname(filePath).toLowerCase();
            const format = this.getImageFormat(ext);
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: this.getMimeType(ext),
                size: stats.size,
                type: document_1.DocumentType.IMAGE,
                format,
                hasText: false,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime
            };
            try {
                const dimensions = await this.getImageDimensions(filePath);
                metadata.width = dimensions.width;
                metadata.height = dimensions.height;
            }
            catch (error) {
                logger_1.logger.debug(`Could not extract image dimensions for ${filePath}:`, error);
            }
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract image metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    getImageFormat(ext) {
        const formatMap = {
            '.jpg': 'JPEG',
            '.jpeg': 'JPEG',
            '.png': 'PNG',
            '.gif': 'GIF',
            '.bmp': 'BMP',
            '.tiff': 'TIFF',
            '.tif': 'TIFF'
        };
        return formatMap[ext] || 'UNKNOWN';
    }
    getMimeType(ext) {
        const mimeMap = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.tiff': 'image/tiff',
            '.tif': 'image/tiff'
        };
        return mimeMap[ext] || 'image/unknown';
    }
    async getImageDimensions(filePath) {
        return new Promise((resolve, reject) => {
            try {
                resolve({ width: 0, height: 0 });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    createChunks(text, chunkSize, overlap) {
        const chunks = [];
        let currentPos = 0;
        while (currentPos < text.length) {
            const endPos = Math.min(currentPos + chunkSize, text.length);
            const chunkText = text.substring(currentPos, endPos);
            chunks.push({
                id: `image_ocr_chunk_${chunks.length}`,
                text: chunkText,
                startIndex: currentPos,
                endIndex: endPos,
                metadata: {
                    source: 'image_ocr',
                    chunkIndex: chunks.length
                }
            });
            currentPos = endPos - overlap;
            if (currentPos >= text.length)
                break;
        }
        return chunks;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        const ext = path_1.default.extname(filePath).toLowerCase();
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: this.getMimeType(ext),
            size: stats.size,
            type: document_1.DocumentType.IMAGE,
            format: this.getImageFormat(ext),
            hasText: false,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.ImageProcessor = ImageProcessor;
//# sourceMappingURL=imageProcessor.js.map