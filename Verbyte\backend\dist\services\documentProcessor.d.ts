import { TextExtractionResult, ProcessingOptions, DocumentProcessorConfig, BatchProcessingResult } from '@/types/document';
export declare class DocumentProcessingService {
    private processors;
    private config;
    constructor(config?: Partial<DocumentProcessorConfig>);
    private initializeProcessors;
    processDocument(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    processBatch(filePaths: string[], options?: ProcessingOptions): Promise<BatchProcessingResult>;
    private getDocumentType;
    isSupported(filePath: string): boolean;
    getSupportedExtensions(): string[];
    private createErrorMetadata;
    getConfig(): DocumentProcessorConfig;
    updateConfig(config: Partial<DocumentProcessorConfig>): void;
}
//# sourceMappingURL=documentProcessor.d.ts.map