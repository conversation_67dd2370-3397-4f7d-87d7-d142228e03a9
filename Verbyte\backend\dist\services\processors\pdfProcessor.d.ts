import { DocumentType, TextExtractionResult, ProcessingOptions, IDocumentProcessor } from '@/types/document';
export declare class PDFProcessor implements IDocumentProcessor {
    constructor();
    supports(mimeType: string): boolean;
    getType(): DocumentType;
    process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    private extractMetadata;
    private extractTextFromPages;
    private createChunks;
    private countWords;
    private createBasicMetadata;
    private createErrorMetadata;
}
//# sourceMappingURL=pdfProcessor.d.ts.map