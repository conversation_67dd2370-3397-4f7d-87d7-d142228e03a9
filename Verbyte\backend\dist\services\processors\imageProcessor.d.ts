import { DocumentType, TextExtractionResult, ProcessingOptions, IDocumentProcessor } from '@/types/document';
export declare class ImageProcessor implements IDocumentProcessor {
    supports(mimeType: string): boolean;
    getType(): DocumentType;
    process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    private performOCR;
    private extractMetadata;
    private getImageFormat;
    private getMimeType;
    private getImageDimensions;
    private createChunks;
    private countWords;
    private createBasicMetadata;
    private createErrorMetadata;
}
//# sourceMappingURL=imageProcessor.d.ts.map