"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ingestRoutes = void 0;
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const upload_1 = require("@/middleware/upload");
const uploadService_1 = require("@/services/uploadService");
const documentProcessingService_1 = require("@/services/documentProcessingService");
const fileCleanup_1 = require("@/utils/fileCleanup");
const logger_1 = require("@/utils/logger");
const router = (0, express_1.Router)();
exports.ingestRoutes = router;
const uploadService = new uploadService_1.UploadService();
const documentProcessingService = new documentProcessingService_1.IntegratedDocumentProcessingService();
router.post('/file', upload_1.uploadSingleFile, upload_1.validateUploadedFile, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        if (!req.file) {
            res.status(400).json({
                status: 'error',
                message: 'No file uploaded',
                code: 'NO_FILE'
            });
            return;
        }
        const upload = await uploadService.createUpload({
            filename: req.file.filename,
            originalName: req.file.originalname,
            mimeType: req.file.mimetype,
            size: req.file.size,
            path: req.file.path,
            userId: req.user?.id
        });
        logger_1.logger.info(`File uploaded successfully: ${req.file.originalname} (${upload._id})`);
        res.status(201).json({
            status: 'success',
            message: 'File uploaded successfully',
            data: {
                uploadId: upload._id,
                filename: upload.originalName,
                size: upload.size,
                mimeType: upload.mimeType,
                status: upload.status,
                processingStatus: upload.processingStatus,
                uploadProgress: upload.uploadProgress,
                processingProgress: upload.processingProgress,
                createdAt: upload.createdAt
            }
        });
    }
    catch (error) {
        logger_1.logger.error('File upload error:', error);
        throw error;
    }
}));
router.post('/files', upload_1.uploadMultipleFiles, upload_1.validateUploadedFile, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
            res.status(400).json({
                status: 'error',
                message: 'No files uploaded',
                code: 'NO_FILES'
            });
            return;
        }
        const uploads = await Promise.all(req.files.map(file => uploadService.createUpload({
            filename: file.filename,
            originalName: file.originalname,
            mimeType: file.mimetype,
            size: file.size,
            path: file.path,
            userId: req.user?.id
        })));
        logger_1.logger.info(`${uploads.length} files uploaded successfully`);
        res.status(201).json({
            status: 'success',
            message: `${uploads.length} files uploaded successfully`,
            data: uploads.map(upload => ({
                uploadId: upload._id,
                filename: upload.originalName,
                size: upload.size,
                mimeType: upload.mimeType,
                status: upload.status,
                processingStatus: upload.processingStatus,
                uploadProgress: upload.uploadProgress,
                processingProgress: upload.processingProgress,
                createdAt: upload.createdAt
            }))
        });
    }
    catch (error) {
        logger_1.logger.error('Multiple files upload error:', error);
        throw error;
    }
}));
router.post('/process/:uploadId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { uploadId } = req.params;
        const options = {
            extractImages: req.body.extractImages || false,
            preserveFormatting: req.body.preserveFormatting || true,
            includeMetadata: req.body.includeMetadata || true,
            chunkSize: req.body.chunkSize || 1000,
            chunkOverlap: req.body.chunkOverlap || 200,
            ocrLanguage: req.body.ocrLanguage || 'eng',
            csvHasHeaders: req.body.csvHasHeaders !== false,
            timeout: req.body.timeout || 300000
        };
        const result = await documentProcessingService.processUploadedDocument(uploadId, options);
        res.json({
            status: 'success',
            message: 'Document processed successfully',
            data: {
                uploadId,
                success: result.success,
                text: result.text,
                metadata: result.metadata,
                chunks: result.chunks,
                processingTime: result.processingTime,
                error: result.error
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Document processing error:', error);
        throw error;
    }
}));
router.post('/process-batch', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { uploadIds } = req.body;
        if (!Array.isArray(uploadIds) || uploadIds.length === 0) {
            res.status(400).json({
                status: 'error',
                message: 'uploadIds array is required',
                code: 'INVALID_INPUT'
            });
            return;
        }
        const options = {
            extractImages: req.body.extractImages || false,
            preserveFormatting: req.body.preserveFormatting || true,
            includeMetadata: req.body.includeMetadata || true,
            chunkSize: req.body.chunkSize || 1000,
            chunkOverlap: req.body.chunkOverlap || 200,
            ocrLanguage: req.body.ocrLanguage || 'eng',
            csvHasHeaders: req.body.csvHasHeaders !== false,
            timeout: req.body.timeout || 300000
        };
        const results = await documentProcessingService.processBatchUploads(uploadIds, options);
        res.json({
            status: 'success',
            message: `Processed ${results.length} documents`,
            data: {
                results: results.map(r => ({
                    uploadId: r.uploadId,
                    success: r.result.success,
                    text: r.result.text,
                    metadata: r.result.metadata,
                    chunks: r.result.chunks,
                    processingTime: r.result.processingTime,
                    error: r.result.error
                })),
                summary: {
                    total: results.length,
                    successful: results.filter(r => r.result.success).length,
                    failed: results.filter(r => !r.result.success).length
                }
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Batch processing error:', error);
        throw error;
    }
}));
router.post('/process-pending', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const options = {
            extractImages: req.body.extractImages || false,
            preserveFormatting: req.body.preserveFormatting || true,
            includeMetadata: req.body.includeMetadata || true,
            chunkSize: req.body.chunkSize || 1000,
            chunkOverlap: req.body.chunkOverlap || 200,
            ocrLanguage: req.body.ocrLanguage || 'eng',
            csvHasHeaders: req.body.csvHasHeaders !== false,
            timeout: req.body.timeout || 300000
        };
        const processedCount = await documentProcessingService.processPendingUploads(options);
        res.json({
            status: 'success',
            message: `Processed ${processedCount} pending uploads`,
            data: {
                processedCount
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Process pending uploads error:', error);
        throw error;
    }
}));
router.post('/text', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { text, filename, metadata } = req.body;
        if (!text || typeof text !== 'string') {
            res.status(400).json({
                status: 'error',
                message: 'Text content is required',
                code: 'INVALID_INPUT'
            });
            return;
        }
        const fs = require('fs');
        const path = require('path');
        const tempDir = path.join(process.cwd(), 'temp');
        const tempFilename = `text_${Date.now()}.txt`;
        const tempPath = path.join(tempDir, tempFilename);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        fs.writeFileSync(tempPath, text, 'utf-8');
        try {
            const upload = await uploadService.createUpload({
                filename: tempFilename,
                originalName: filename || 'text_content.txt',
                mimeType: 'text/plain',
                size: Buffer.byteLength(text, 'utf-8'),
                path: tempPath,
                userId: req.user?.id
            });
            const options = {
                preserveFormatting: req.body.preserveFormatting || true,
                includeMetadata: req.body.includeMetadata || true,
                chunkSize: req.body.chunkSize || 1000,
                chunkOverlap: req.body.chunkOverlap || 200
            };
            const result = await documentProcessingService.processUploadedDocument(upload._id.toString(), options);
            fs.unlinkSync(tempPath);
            res.json({
                status: 'success',
                message: 'Text content processed successfully',
                data: {
                    uploadId: upload._id,
                    success: result.success,
                    text: result.text,
                    metadata: result.metadata,
                    chunks: result.chunks,
                    processingTime: result.processingTime,
                    error: result.error
                }
            });
        }
        catch (error) {
            if (fs.existsSync(tempPath)) {
                fs.unlinkSync(tempPath);
            }
            throw error;
        }
    }
    catch (error) {
        logger_1.logger.error('Text ingestion error:', error);
        throw error;
    }
}));
router.get('/status/:uploadId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { uploadId } = req.params;
        const progress = await uploadService.getProgress(uploadId);
        res.json({
            status: 'success',
            data: progress
        });
    }
    catch (error) {
        logger_1.logger.error('Get upload status error:', error);
        throw error;
    }
}));
router.get('/uploads', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 50;
        const offset = parseInt(req.query.offset) || 0;
        const uploads = await uploadService.getAllUploads(limit, offset);
        res.json({
            status: 'success',
            data: uploads.map(upload => ({
                uploadId: upload._id,
                filename: upload.originalName,
                size: upload.size,
                mimeType: upload.mimeType,
                status: upload.status,
                processingStatus: upload.processingStatus,
                uploadProgress: upload.uploadProgress,
                processingProgress: upload.processingProgress,
                error: upload.error?.message,
                createdAt: upload.createdAt,
                completedAt: upload.completedAt
            })),
            pagination: {
                limit,
                offset,
                total: uploads.length
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Get uploads error:', error);
        throw error;
    }
}));
router.delete('/uploads/:uploadId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { uploadId } = req.params;
        await uploadService.deleteUpload(uploadId);
        res.json({
            status: 'success',
            message: 'Upload deleted successfully'
        });
    }
    catch (error) {
        logger_1.logger.error('Delete upload error:', error);
        throw error;
    }
}));
router.post('/reprocess/:uploadId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const { uploadId } = req.params;
        const options = {
            extractImages: req.body.extractImages || false,
            preserveFormatting: req.body.preserveFormatting || true,
            includeMetadata: req.body.includeMetadata || true,
            chunkSize: req.body.chunkSize || 1000,
            chunkOverlap: req.body.chunkOverlap || 200,
            ocrLanguage: req.body.ocrLanguage || 'eng',
            csvHasHeaders: req.body.csvHasHeaders !== false,
            timeout: req.body.timeout || 300000
        };
        const result = await documentProcessingService.reprocessUpload(uploadId, options);
        res.json({
            status: 'success',
            message: 'Document reprocessed successfully',
            data: {
                uploadId,
                success: result.success,
                text: result.text,
                metadata: result.metadata,
                chunks: result.chunks,
                processingTime: result.processingTime,
                error: result.error
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Document reprocessing error:', error);
        throw error;
    }
}));
router.get('/supported-types', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const supportedExtensions = documentProcessingService.getSupportedExtensions();
        const config = documentProcessingService.getProcessorConfig();
        res.json({
            status: 'success',
            data: {
                supportedExtensions,
                supportedTypes: config.supportedTypes,
                maxFileSize: config.maxFileSize,
                ocrEnabled: config.ocrEnabled
            }
        });
    }
    catch (error) {
        logger_1.logger.error('Get supported types error:', error);
        throw error;
    }
}));
router.get('/processing-stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const stats = await documentProcessingService.getProcessingStats();
        res.json({
            status: 'success',
            data: stats
        });
    }
    catch (error) {
        logger_1.logger.error('Get processing stats error:', error);
        throw error;
    }
}));
router.get('/stats', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const stats = await uploadService.getUploadStats();
        res.json({
            status: 'success',
            data: stats
        });
    }
    catch (error) {
        logger_1.logger.error('Get upload stats error:', error);
        throw error;
    }
}));
router.post('/cleanup', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const result = await fileCleanup_1.fileCleanupService.runManualCleanup();
        res.json({
            status: 'success',
            message: 'Cleanup completed successfully',
            data: result
        });
    }
    catch (error) {
        logger_1.logger.error('Manual cleanup error:', error);
        throw error;
    }
}));
router.get('/disk-usage', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    try {
        const usage = await fileCleanup_1.fileCleanupService.getDiskUsage();
        res.json({
            status: 'success',
            data: usage
        });
    }
    catch (error) {
        logger_1.logger.error('Get disk usage error:', error);
        throw error;
    }
}));
//# sourceMappingURL=ingest.js.map