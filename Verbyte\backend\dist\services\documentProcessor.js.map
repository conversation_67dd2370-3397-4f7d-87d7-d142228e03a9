{"version": 3, "file": "documentProcessor.js", "sourceRoot": "", "sources": ["../../src/services/documentProcessor.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,2CAAwC;AACxC,+CAW0B;AAG1B,4DAAyD;AACzD,8DAA2D;AAC3D,4DAAyD;AACzD,sEAAmE;AACnE,8DAA2D;AAC3D,gEAA6D;AAC7D,0EAAuE;AACvE,8DAA2D;AAE3D,MAAa,yBAAyB;IAIpC,YAAY,MAAyC;QACnD,IAAI,CAAC,MAAM,GAAG;YACZ,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;YAC9B,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,uBAAY,CAAC;YAC3C,cAAc,EAAE;gBACd,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,eAAe,EAAE,IAAI;gBACrB,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,GAAG;gBACjB,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,IAAI;gBACnB,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;gBAC9B,OAAO,EAAE,MAAM;aAChB;YACD,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC;YAC/C,sBAAsB,EAAE,IAAI;YAC5B,GAAG,MAAM;SACV,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAKO,oBAAoB;QAC1B,IAAI,CAAC;YACH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,GAAG,EAAE,IAAI,2BAAY,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,IAAI,EAAE,IAAI,6BAAa,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,GAAG,EAAE,IAAI,2BAAY,EAAE,CAAC,CAAC;YAC1D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,QAAQ,EAAE,IAAI,qCAAiB,EAAE,CAAC,CAAC;YACpE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,IAAI,EAAE,IAAI,6BAAa,EAAE,CAAC,CAAC;YAC5D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,KAAK,EAAE,IAAI,+BAAc,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,UAAU,EAAE,IAAI,yCAAmB,EAAE,CAAC,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAY,CAAC,IAAI,EAAE,IAAI,6BAAa,EAAE,CAAC,CAAC;YAE5D,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CACnB,QAAgB,EAChB,OAA2B;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;QAEpE,IAAI,CAAC;YAEH,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;YAGD,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpC,IAAI,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC,WAAY,EAAE,CAAC;gBAC5C,MAAM,IAAI,KAAK,CAAC,mBAAmB,KAAK,CAAC,IAAI,gBAAgB,aAAa,CAAC,WAAW,SAAS,CAAC,CAAC;YACnG,CAAC;YAGD,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CAAC,0BAA0B,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtE,CAAC;YAGD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACpD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,6CAA6C,YAAY,EAAE,CAAC,CAAC;YAC/E,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,WAAW,YAAY,GAAG,CAAC,CAAC;YAGxE,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAGhE,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE/C,eAAM,CAAC,IAAI,CAAC,oCAAoC,QAAQ,KAAK,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;YACzF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAE9E,eAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAE/D,OAAO;gBACL,IAAI,EAAE,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC;gBAC1D,cAAc;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAChB,SAAmB,EACnB,OAA2B;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAsB,EAAE,CAAC;QAErC,eAAM,CAAC,IAAI,CAAC,gCAAgC,SAAS,CAAC,MAAM,YAAY,CAAC,CAAC;QAE1E,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC7D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,EAAE,kCAAuB,CAAC,iBAAiB;wBAC/C,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI,mBAAmB;wBAC5C,OAAO,EAAE,QAAQ;wBACjB,KAAK,EAAE,0BAAe,CAAC,eAAe;wBACtC,SAAS,EAAE,IAAI,IAAI,EAAE;qBACtB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9E,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,kCAAuB,CAAC,aAAa;oBAC3C,OAAO,EAAE,YAAY;oBACrB,OAAO,EAAE,QAAQ;oBACjB,KAAK,EAAE,0BAAe,CAAC,eAAe;oBACtC,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBAGH,OAAO,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,EAAE;oBACR,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC;oBAC1D,cAAc,EAAE,CAAC;oBACjB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,YAAY;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC9C,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAC9D,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC;QAErD,eAAM,CAAC,IAAI,CAAC,+BAA+B,eAAe,gBAAgB,WAAW,YAAY,cAAc,KAAK,CAAC,CAAC;QAEtH,OAAO;YACL,UAAU,EAAE,SAAS,CAAC,MAAM;YAC5B,eAAe;YACf,WAAW;YACX,OAAO;YACP,MAAM;YACN,cAAc;SACf,CAAC;IACJ,CAAC;IAKO,eAAe,CAAC,QAAgB;QACtC,MAAM,GAAG,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,WAAW,GAAiC;YAChD,MAAM,EAAE,uBAAY,CAAC,GAAG;YACxB,OAAO,EAAE,uBAAY,CAAC,IAAI;YAC1B,MAAM,EAAE,uBAAY,CAAC,IAAI;YACzB,MAAM,EAAE,uBAAY,CAAC,GAAG;YACxB,KAAK,EAAE,uBAAY,CAAC,QAAQ;YAC5B,WAAW,EAAE,uBAAY,CAAC,QAAQ;YAClC,OAAO,EAAE,uBAAY,CAAC,IAAI;YAC1B,MAAM,EAAE,uBAAY,CAAC,KAAK;YAC1B,OAAO,EAAE,uBAAY,CAAC,KAAK;YAC3B,MAAM,EAAE,uBAAY,CAAC,KAAK;YAC1B,MAAM,EAAE,uBAAY,CAAC,KAAK;YAC1B,MAAM,EAAE,uBAAY,CAAC,KAAK;YAC1B,OAAO,EAAE,uBAAY,CAAC,KAAK;YAC3B,OAAO,EAAE,uBAAY,CAAC,UAAU;YAChC,MAAM,EAAE,uBAAY,CAAC,UAAU;YAC/B,MAAM,EAAE,uBAAY,CAAC,IAAI;YACzB,OAAO,EAAE,uBAAY,CAAC,IAAI;SAC3B,CAAC;QAEF,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IAClC,CAAC;IAKD,WAAW,CAAC,QAAgB;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC;IACjD,CAAC;IAKD,sBAAsB;QACpB,OAAO;YACL,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO;YAC5D,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;YAChD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;SACjC,CAAC;IACJ,CAAC;IAKO,mBAAmB,CAAC,QAAgB,EAAE,KAAa;QACzD,MAAM,KAAK,GAAG,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAErE,OAAO;YACL,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrC,QAAQ,EAAE,0BAA0B;YACpC,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC;YACtB,IAAI,EAAE,uBAAY,CAAC,IAAI;YACvB,SAAS,EAAE,CAAC;YACZ,cAAc,EAAE,CAAC;YACjB,KAAK;SACN,CAAC;IACJ,CAAC;IAKD,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAKD,YAAY,CAAC,MAAwC;QACnD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;QAC5C,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;IAC1D,CAAC;CACF;AA7PD,8DA6PC"}