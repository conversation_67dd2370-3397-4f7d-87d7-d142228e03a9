"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupTempFiles = exports.validateUploadedFile = exports.uploadMultipleFilesToMemory = exports.uploadSingleFileToMemory = exports.uploadMultipleFiles = exports.uploadSingleFile = exports.uploadToMemory = exports.uploadToDisk = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const crypto_1 = __importDefault(require("crypto"));
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const ensureUploadDir = (dir) => {
    if (!fs_1.default.existsSync(dir)) {
        fs_1.default.mkdirSync(dir, { recursive: true });
        logger_1.logger.info(`Created upload directory: ${dir}`);
    }
};
const fileFilter = (req, file, cb) => {
    try {
        const fileExtension = path_1.default.extname(file.originalname).toLowerCase().slice(1);
        const allowedTypes = environment_1.config.upload.allowedFileTypes;
        if (!allowedTypes.includes(fileExtension)) {
            const error = new errorHandler_1.ValidationError(`File type '${fileExtension}' not allowed. Allowed types: ${allowedTypes.join(', ')}`);
            return cb(error);
        }
        const allowedMimeTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/markdown',
            'application/json',
            'text/csv',
            'image/jpeg',
            'image/png',
            'image/jpg'
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
            const error = new errorHandler_1.ValidationError(`MIME type '${file.mimetype}' not allowed`);
            return cb(error);
        }
        if (file.originalname.includes('..') || file.originalname.includes('/') || file.originalname.includes('\\')) {
            const error = new errorHandler_1.ValidationError('Invalid file name');
            return cb(error);
        }
        cb(null, true);
    }
    catch (error) {
        logger_1.logger.error('File filter error:', error);
        cb(error);
    }
};
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        try {
            const uploadDir = path_1.default.join(process.cwd(), environment_1.config.upload.uploadDir);
            ensureUploadDir(uploadDir);
            cb(null, uploadDir);
        }
        catch (error) {
            logger_1.logger.error('Storage destination error:', error);
            cb(error, '');
        }
    },
    filename: (req, file, cb) => {
        try {
            const timestamp = Date.now();
            const randomString = crypto_1.default.randomBytes(8).toString('hex');
            const fileExtension = path_1.default.extname(file.originalname);
            const filename = `${timestamp}-${randomString}${fileExtension}`;
            if (!req.fileMetadata) {
                req.fileMetadata = {};
            }
            req.fileMetadata.originalName = file.originalname;
            req.fileMetadata.generatedName = filename;
            cb(null, filename);
        }
        catch (error) {
            logger_1.logger.error('Storage filename error:', error);
            cb(error, '');
        }
    }
});
const memoryStorage = multer_1.default.memoryStorage();
exports.uploadToDisk = (0, multer_1.default)({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: environment_1.config.upload.maxFileSize,
        files: 10,
        fields: 20,
    },
});
exports.uploadToMemory = (0, multer_1.default)({
    storage: memoryStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: environment_1.config.upload.maxFileSize,
        files: 10,
        fields: 20,
    },
});
exports.uploadSingleFile = exports.uploadToDisk.single('file');
exports.uploadMultipleFiles = exports.uploadToDisk.array('files', 10);
exports.uploadSingleFileToMemory = exports.uploadToMemory.single('file');
exports.uploadMultipleFilesToMemory = exports.uploadToMemory.array('files', 10);
const validateUploadedFile = (req, res, next) => {
    try {
        if (!req.file && !req.files) {
            throw new errorHandler_1.ValidationError('No file uploaded');
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.validateUploadedFile = validateUploadedFile;
const cleanupTempFiles = (req, res, next) => {
    const cleanup = () => {
        try {
            if (req.file && req.file.path) {
                fs_1.default.unlink(req.file.path, (err) => {
                    if (err) {
                        logger_1.logger.error('Failed to cleanup temp file:', err);
                    }
                    else {
                        logger_1.logger.debug(`Cleaned up temp file: ${req.file.path}`);
                    }
                });
            }
            if (req.files && Array.isArray(req.files)) {
                req.files.forEach((file) => {
                    if (file.path) {
                        fs_1.default.unlink(file.path, (err) => {
                            if (err) {
                                logger_1.logger.error('Failed to cleanup temp file:', err);
                            }
                            else {
                                logger_1.logger.debug(`Cleaned up temp file: ${file.path}`);
                            }
                        });
                    }
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Cleanup error:', error);
        }
    };
    res.on('finish', cleanup);
    res.on('close', cleanup);
    res.on('error', cleanup);
    next();
};
exports.cleanupTempFiles = cleanupTempFiles;
//# sourceMappingURL=upload.js.map