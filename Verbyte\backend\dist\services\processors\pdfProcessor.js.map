{"version": 3, "file": "pdfProcessor.js", "sourceRoot": "", "sources": ["../../../src/services/processors/pdfProcessor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,qDAAuC;AACvC,2CAAwC;AACxC,+CAO0B;AAE1B,MAAa,YAAY;IACvB;QAEE,IAAI,CAAC;YACH,QAAQ,CAAC,mBAAmB,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAC7F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,QAAQ,CAAC,mBAAmB,CAAC,SAAS,GAAG,gCAAgC,CAAC;QAC5E,CAAC;IACH,CAAC;IAKD,QAAQ,CAAC,QAAgB;QACvB,OAAO,QAAQ,KAAK,iBAAiB,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpE,CAAC;IAKD,OAAO;QACL,OAAO,uBAAY,CAAC,GAAG,CAAC;IAC1B,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,QAAgB,EAAE,OAA2B;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;YAGjD,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,YAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;YAGvD,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC;gBAC7C,IAAI;gBACJ,SAAS,EAAE,CAAC;aACb,CAAC,CAAC,OAAO,CAAC;YAGX,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAGnE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAG/E,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3C,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;YAEtC,MAAM,MAAM,GAAyB;gBACnC,IAAI;gBACJ,QAAQ;gBACR,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAC/C,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,IAAI;aACd,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,6BAA6B,QAAQ,KAAK,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;YACnF,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC;YAC7F,eAAM,CAAC,KAAK,CAAC,0BAA0B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAE1D,OAAO;gBACL,IAAI,EAAE,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC;gBAC1D,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,WAAgB,EAAE,QAAgB;QAC9D,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,WAAW,EAAE,CAAC;YAC7C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEpC,MAAM,QAAQ,GAAgB;gBAC5B,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACrC,QAAQ,EAAE,iBAAiB;gBAC3B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,uBAAY,CAAC,GAAG;gBACtB,KAAK,EAAE,WAAW,CAAC,QAAQ;gBAC3B,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,gBAAgB,IAAI,SAAS;gBACpD,WAAW,EAAE,WAAW,CAAC,WAAW,IAAI,KAAK;gBAC7C,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,SAAS;gBACpC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,SAAS;gBACtC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,SAAS;gBACxC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;gBACtG,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,SAAS;gBACxC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI,SAAS;gBAC1C,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;gBACjF,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;aACzE,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,mCAAmC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAChC,WAAgB,EAChB,OAA2B;QAE3B,MAAM,SAAS,GAAa,EAAE,CAAC;QAC/B,MAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE,CAAC;YACjE,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAChD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;gBAGhD,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK;qBAC/B,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;qBAC5B,IAAI,CAAC,GAAG,CAAC;qBACT,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;qBACpB,IAAI,EAAE,CAAC;gBAEV,IAAI,QAAQ,EAAE,CAAC;oBACb,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAGzB,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;wBACvB,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAClC,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,YAAY,IAAI,CAAC,CAC1B,CAAC;wBACF,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;wBAC3B,YAAY,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;oBACtC,CAAC;gBACH,CAAC;gBAED,eAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,IAAI,WAAW,CAAC,QAAQ,KAAK,QAAQ,CAAC,MAAM,aAAa,CAAC,CAAC;YAC7G,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;YAErE,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;IACpC,CAAC;IAKO,YAAY,CAClB,IAAY,EACZ,UAAkB,EAClB,IAAY,EACZ,SAAiB,EACjB,OAAe;QAEf,MAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAErD,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,YAAY,IAAI,UAAU,MAAM,CAAC,MAAM,EAAE;gBAC7C,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,UAAU,GAAG,UAAU;gBACnC,QAAQ,EAAE,UAAU,GAAG,MAAM;gBAC7B,IAAI;gBACJ,QAAQ,EAAE;oBACR,MAAM,EAAE,KAAK;oBACb,IAAI;oBACJ,UAAU,EAAE,MAAM,CAAC,MAAM;iBAC1B;aACF,CAAC,CAAC;YAGH,UAAU,GAAG,MAAM,GAAG,OAAO,CAAC;YAC9B,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM;gBAAE,MAAM;QACvC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,UAAU,CAAC,IAAY;QAC7B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IACzE,CAAC;IAKO,mBAAmB,CAAC,QAAgB;QAC1C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,OAAO;YACL,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrC,QAAQ,EAAE,iBAAiB;YAC3B,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,uBAAY,CAAC,GAAG;YACtB,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,SAAS;YACrB,WAAW,EAAE,KAAK;SACnB,CAAC;IACJ,CAAC;IAKO,mBAAmB,CAAC,QAAgB,EAAE,KAAa;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO;YACL,GAAG,aAAa;YAChB,KAAK;SACN,CAAC;IACJ,CAAC;CACF;AA5OD,oCA4OC"}