"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Upload = exports.ProcessingStatus = exports.UploadStatus = void 0;
const mongoose_1 = __importStar(require("mongoose"));
var UploadStatus;
(function (UploadStatus) {
    UploadStatus["PENDING"] = "pending";
    UploadStatus["UPLOADING"] = "uploading";
    UploadStatus["UPLOADED"] = "uploaded";
    UploadStatus["PROCESSING"] = "processing";
    UploadStatus["COMPLETED"] = "completed";
    UploadStatus["FAILED"] = "failed";
    UploadStatus["CANCELLED"] = "cancelled";
})(UploadStatus || (exports.UploadStatus = UploadStatus = {}));
var ProcessingStatus;
(function (ProcessingStatus) {
    ProcessingStatus["PENDING"] = "pending";
    ProcessingStatus["EXTRACTING_TEXT"] = "extracting_text";
    ProcessingStatus["CHUNKING"] = "chunking";
    ProcessingStatus["GENERATING_EMBEDDINGS"] = "generating_embeddings";
    ProcessingStatus["STORING_VECTORS"] = "storing_vectors";
    ProcessingStatus["COMPLETED"] = "completed";
    ProcessingStatus["FAILED"] = "failed";
})(ProcessingStatus || (exports.ProcessingStatus = ProcessingStatus = {}));
const uploadSchema = new mongoose_1.Schema({
    userId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: false,
        index: true
    },
    filename: {
        type: String,
        required: true,
        trim: true
    },
    originalName: {
        type: String,
        required: true,
        trim: true
    },
    mimeType: {
        type: String,
        required: true,
        trim: true
    },
    size: {
        type: Number,
        required: true,
        min: 0
    },
    path: {
        type: String,
        required: true,
        trim: true
    },
    status: {
        type: String,
        enum: Object.values(UploadStatus),
        default: UploadStatus.PENDING,
        required: true,
        index: true
    },
    processingStatus: {
        type: String,
        enum: Object.values(ProcessingStatus),
        default: ProcessingStatus.PENDING,
        required: true,
        index: true
    },
    uploadProgress: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    processingProgress: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    metadata: {
        chunks: {
            type: Number,
            min: 0
        },
        pages: {
            type: Number,
            min: 0
        },
        extractedText: {
            type: Boolean,
            default: false
        },
        embeddingsGenerated: {
            type: Boolean,
            default: false
        },
        vectorsStored: {
            type: Boolean,
            default: false
        },
        qdrantCollectionId: {
            type: String,
            trim: true
        },
        documentIds: [{
                type: String,
                trim: true
            }]
    },
    error: {
        message: {
            type: String,
            trim: true
        },
        code: {
            type: String,
            trim: true
        },
        stack: {
            type: String
        },
        timestamp: {
            type: Date,
            default: Date.now
        }
    },
    startedAt: {
        type: Date,
        default: Date.now,
        required: true
    },
    completedAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        transform: (doc, ret) => {
            ret.id = ret._id;
            delete ret._id;
            delete ret.__v;
            return ret;
        }
    }
});
uploadSchema.index({ userId: 1, createdAt: -1 });
uploadSchema.index({ status: 1, createdAt: -1 });
uploadSchema.index({ processingStatus: 1, createdAt: -1 });
uploadSchema.index({ filename: 1 });
uploadSchema.index({ originalName: 'text' });
uploadSchema.methods.updateProgress = function (uploadProgress, processingProgress) {
    if (uploadProgress !== undefined) {
        this.uploadProgress = Math.max(0, Math.min(100, uploadProgress));
    }
    if (processingProgress !== undefined) {
        this.processingProgress = Math.max(0, Math.min(100, processingProgress));
    }
    return this.save();
};
uploadSchema.methods.setStatus = function (status, processingStatus) {
    this.status = status;
    if (processingStatus) {
        this.processingStatus = processingStatus;
    }
    if (status === UploadStatus.COMPLETED) {
        this.completedAt = new Date();
        this.uploadProgress = 100;
        this.processingProgress = 100;
    }
    return this.save();
};
uploadSchema.methods.setError = function (error, code) {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;
    this.error = {
        message: errorMessage,
        code: code || 'UNKNOWN_ERROR',
        stack: errorStack,
        timestamp: new Date()
    };
    this.status = UploadStatus.FAILED;
    this.processingStatus = ProcessingStatus.FAILED;
    return this.save();
};
uploadSchema.statics.findByUserId = function (userId) {
    return this.find({ userId }).sort({ createdAt: -1 });
};
uploadSchema.statics.findByStatus = function (status) {
    return this.find({ status }).sort({ createdAt: -1 });
};
uploadSchema.statics.findPendingProcessing = function () {
    return this.find({
        status: UploadStatus.UPLOADED,
        processingStatus: ProcessingStatus.PENDING
    }).sort({ createdAt: 1 });
};
uploadSchema.pre('save', function (next) {
    this.uploadProgress = Math.max(0, Math.min(100, this.uploadProgress));
    this.processingProgress = Math.max(0, Math.min(100, this.processingProgress));
    next();
});
exports.Upload = mongoose_1.default.model('Upload', uploadSchema);
//# sourceMappingURL=Upload.js.map