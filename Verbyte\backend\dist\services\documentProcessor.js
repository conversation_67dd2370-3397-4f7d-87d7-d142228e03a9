"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentProcessingService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
const pdfProcessor_1 = require("./processors/pdfProcessor");
const docxProcessor_1 = require("./processors/docxProcessor");
const csvProcessor_1 = require("./processors/csvProcessor");
const markdownProcessor_1 = require("./processors/markdownProcessor");
const jsonProcessor_1 = require("./processors/jsonProcessor");
const imageProcessor_1 = require("./processors/imageProcessor");
const powerPointProcessor_1 = require("./processors/powerPointProcessor");
const textProcessor_1 = require("./processors/textProcessor");
class DocumentProcessingService {
    constructor(config) {
        this.config = {
            maxFileSize: 100 * 1024 * 1024,
            supportedTypes: Object.values(document_1.DocumentType),
            defaultOptions: {
                extractImages: false,
                preserveFormatting: true,
                includeMetadata: true,
                chunkSize: 1000,
                chunkOverlap: 200,
                ocrLanguage: 'eng',
                csvHasHeaders: true,
                maxFileSize: 100 * 1024 * 1024,
                timeout: 300000
            },
            ocrEnabled: true,
            tempDirectory: path_1.default.join(process.cwd(), 'temp'),
            cleanupAfterProcessing: true,
            ...config
        };
        this.processors = new Map();
        this.initializeProcessors();
    }
    initializeProcessors() {
        try {
            this.processors.set(document_1.DocumentType.PDF, new pdfProcessor_1.PDFProcessor());
            this.processors.set(document_1.DocumentType.DOCX, new docxProcessor_1.DOCXProcessor());
            this.processors.set(document_1.DocumentType.CSV, new csvProcessor_1.CSVProcessor());
            this.processors.set(document_1.DocumentType.MARKDOWN, new markdownProcessor_1.MarkdownProcessor());
            this.processors.set(document_1.DocumentType.JSON, new jsonProcessor_1.JSONProcessor());
            this.processors.set(document_1.DocumentType.IMAGE, new imageProcessor_1.ImageProcessor());
            this.processors.set(document_1.DocumentType.POWERPOINT, new powerPointProcessor_1.PowerPointProcessor());
            this.processors.set(document_1.DocumentType.TEXT, new textProcessor_1.TextProcessor());
            logger_1.logger.info('Document processors initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize document processors:', error);
            throw error;
        }
    }
    async processDocument(filePath, options) {
        const startTime = Date.now();
        const mergedOptions = { ...this.config.defaultOptions, ...options };
        try {
            if (!fs_1.default.existsSync(filePath)) {
                throw new Error(`File not found: ${filePath}`);
            }
            const stats = fs_1.default.statSync(filePath);
            if (stats.size > mergedOptions.maxFileSize) {
                throw new Error(`File too large: ${stats.size} bytes (max: ${mergedOptions.maxFileSize} bytes)`);
            }
            const documentType = this.getDocumentType(filePath);
            if (!documentType) {
                throw new Error(`Unsupported file type: ${path_1.default.extname(filePath)}`);
            }
            const processor = this.processors.get(documentType);
            if (!processor) {
                throw new Error(`No processor available for document type: ${documentType}`);
            }
            logger_1.logger.info(`Processing document: ${filePath} (type: ${documentType})`);
            const result = await processor.process(filePath, mergedOptions);
            result.processingTime = Date.now() - startTime;
            logger_1.logger.info(`Document processed successfully: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            logger_1.logger.error(`Document processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime,
                success: false,
                error: errorMessage
            };
        }
    }
    async processBatch(filePaths, options) {
        const startTime = Date.now();
        const results = [];
        const errors = [];
        logger_1.logger.info(`Starting batch processing of ${filePaths.length} documents`);
        for (const filePath of filePaths) {
            try {
                const result = await this.processDocument(filePath, options);
                results.push(result);
                if (!result.success) {
                    errors.push({
                        code: document_1.DocumentProcessingError.EXTRACTION_FAILED,
                        message: result.error || 'Processing failed',
                        details: filePath,
                        stage: document_1.ProcessingStage.EXTRACTING_TEXT,
                        timestamp: new Date()
                    });
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                errors.push({
                    code: document_1.DocumentProcessingError.UNKNOWN_ERROR,
                    message: errorMessage,
                    details: filePath,
                    stage: document_1.ProcessingStage.EXTRACTING_TEXT,
                    timestamp: new Date()
                });
                results.push({
                    text: '',
                    metadata: this.createErrorMetadata(filePath, errorMessage),
                    processingTime: 0,
                    success: false,
                    error: errorMessage
                });
            }
        }
        const processingTime = Date.now() - startTime;
        const successfulFiles = results.filter(r => r.success).length;
        const failedFiles = results.length - successfulFiles;
        logger_1.logger.info(`Batch processing completed: ${successfulFiles} successful, ${failedFiles} failed (${processingTime}ms)`);
        return {
            totalFiles: filePaths.length,
            successfulFiles,
            failedFiles,
            results,
            errors,
            processingTime
        };
    }
    getDocumentType(filePath) {
        const ext = path_1.default.extname(filePath).toLowerCase();
        const mimeTypeMap = {
            '.pdf': document_1.DocumentType.PDF,
            '.docx': document_1.DocumentType.DOCX,
            '.doc': document_1.DocumentType.DOCX,
            '.csv': document_1.DocumentType.CSV,
            '.md': document_1.DocumentType.MARKDOWN,
            '.markdown': document_1.DocumentType.MARKDOWN,
            '.json': document_1.DocumentType.JSON,
            '.jpg': document_1.DocumentType.IMAGE,
            '.jpeg': document_1.DocumentType.IMAGE,
            '.png': document_1.DocumentType.IMAGE,
            '.gif': document_1.DocumentType.IMAGE,
            '.bmp': document_1.DocumentType.IMAGE,
            '.tiff': document_1.DocumentType.IMAGE,
            '.pptx': document_1.DocumentType.POWERPOINT,
            '.ppt': document_1.DocumentType.POWERPOINT,
            '.txt': document_1.DocumentType.TEXT,
            '.text': document_1.DocumentType.TEXT
        };
        return mimeTypeMap[ext] || null;
    }
    isSupported(filePath) {
        return this.getDocumentType(filePath) !== null;
    }
    getSupportedExtensions() {
        return [
            '.pdf', '.docx', '.doc', '.csv', '.md', '.markdown', '.json',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff',
            '.pptx', '.ppt', '.txt', '.text'
        ];
    }
    createErrorMetadata(filePath, error) {
        const stats = fs_1.default.existsSync(filePath) ? fs_1.default.statSync(filePath) : null;
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'application/octet-stream',
            size: stats?.size || 0,
            type: document_1.DocumentType.TEXT,
            wordCount: 0,
            characterCount: 0,
            error
        };
    }
    getConfig() {
        return { ...this.config };
    }
    updateConfig(config) {
        this.config = { ...this.config, ...config };
        logger_1.logger.info('Document processor configuration updated');
    }
}
exports.DocumentProcessingService = DocumentProcessingService;
//# sourceMappingURL=documentProcessor.js.map