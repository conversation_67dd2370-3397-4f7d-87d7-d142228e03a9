"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getQdrantStatus = exports.disconnectQdrant = exports.clearCollection = exports.checkQdrantHealth = exports.getCollectionInfo = exports.ensureCollection = exports.getQdrantClient = exports.connectQdrant = void 0;
const js_client_rest_1 = require("@qdrant/js-client-rest");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
let qdrantClient = null;
let isConnected = false;
const initializeQdrantClient = () => {
    const clientConfig = {
        url: environment_1.config.qdrant.url,
    };
    if (environment_1.config.qdrant.apiKey) {
        clientConfig.apiKey = environment_1.config.qdrant.apiKey;
    }
    return new js_client_rest_1.QdrantClient(clientConfig);
};
const connectQdrant = async () => {
    try {
        if (isConnected && qdrantClient) {
            logger_1.logger.info('Qdrant already connected');
            return;
        }
        qdrantClient = initializeQdrantClient();
        await qdrantClient.getCollections();
        isConnected = true;
        logger_1.logger.info('✅ Connected to Qdrant successfully');
        await (0, exports.ensureCollection)();
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to Qdrant:', error);
        isConnected = false;
        qdrantClient = null;
        throw error;
    }
};
exports.connectQdrant = connectQdrant;
const getQdrantClient = () => {
    if (!qdrantClient || !isConnected) {
        throw new Error('Qdrant client not initialized. Call connectQdrant() first.');
    }
    return qdrantClient;
};
exports.getQdrantClient = getQdrantClient;
const ensureCollection = async () => {
    try {
        const client = (0, exports.getQdrantClient)();
        const collectionName = environment_1.config.qdrant.collectionName;
        const collections = await client.getCollections();
        const collectionExists = collections.collections.some((collection) => collection.name === collectionName);
        if (!collectionExists) {
            logger_1.logger.info(`Creating Qdrant collection: ${collectionName}`);
            await client.createCollection(collectionName, {
                vectors: {
                    size: environment_1.config.embedding.dimension,
                    distance: 'Cosine',
                },
                optimizers_config: {
                    default_segment_number: 2,
                },
                replication_factor: 1,
            });
            logger_1.logger.info(`✅ Created Qdrant collection: ${collectionName}`);
        }
        else {
            logger_1.logger.info(`Qdrant collection already exists: ${collectionName}`);
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to ensure Qdrant collection:', error);
        throw error;
    }
};
exports.ensureCollection = ensureCollection;
const getCollectionInfo = async () => {
    try {
        const client = (0, exports.getQdrantClient)();
        const collectionName = environment_1.config.qdrant.collectionName;
        const info = await client.getCollection(collectionName);
        return info;
    }
    catch (error) {
        logger_1.logger.error('Failed to get collection info:', error);
        throw error;
    }
};
exports.getCollectionInfo = getCollectionInfo;
const checkQdrantHealth = async () => {
    try {
        if (!isConnected || !qdrantClient) {
            return {
                status: 'unhealthy',
                message: 'Qdrant not connected',
            };
        }
        const collections = await qdrantClient.getCollections();
        const collectionInfo = await (0, exports.getCollectionInfo)();
        return {
            status: 'healthy',
            message: 'Qdrant connection is healthy',
            details: {
                collections: collections,
                collection: {
                    name: environment_1.config.qdrant.collectionName,
                    status: collectionInfo.status,
                    vectorsCount: collectionInfo.vectors_count,
                    pointsCount: collectionInfo.points_count,
                },
            },
        };
    }
    catch (error) {
        logger_1.logger.error('Qdrant health check failed:', error);
        return {
            status: 'unhealthy',
            message: 'Qdrant health check failed',
            details: {
                error: error instanceof Error ? error.message : 'Unknown error',
            },
        };
    }
};
exports.checkQdrantHealth = checkQdrantHealth;
const clearCollection = async () => {
    if (environment_1.config.env !== 'test') {
        throw new Error('clearCollection can only be used in test environment');
    }
    try {
        const client = (0, exports.getQdrantClient)();
        const collectionName = environment_1.config.qdrant.collectionName;
        await client.delete(collectionName, {
            filter: {
                must: [
                    {
                        key: 'document_id',
                        match: {
                            any: ['*'],
                        },
                    },
                ],
            },
        });
        logger_1.logger.info('Qdrant collection cleared successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to clear Qdrant collection:', error);
        throw error;
    }
};
exports.clearCollection = clearCollection;
const disconnectQdrant = async () => {
    try {
        if (qdrantClient) {
            qdrantClient = null;
            isConnected = false;
            logger_1.logger.info('✅ Disconnected from Qdrant successfully');
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to disconnect from Qdrant:', error);
        throw error;
    }
};
exports.disconnectQdrant = disconnectQdrant;
const getQdrantStatus = () => {
    return {
        connected: isConnected,
        url: environment_1.config.qdrant.url,
        collectionName: environment_1.config.qdrant.collectionName,
    };
};
exports.getQdrantStatus = getQdrantStatus;
//# sourceMappingURL=qdrant.js.map