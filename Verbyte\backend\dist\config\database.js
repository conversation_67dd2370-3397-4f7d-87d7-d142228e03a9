"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearDatabase = exports.checkDatabaseHealth = exports.getDatabaseStatus = exports.disconnectDatabase = exports.connectDatabase = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const mongooseOptions = {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    bufferCommands: false,
};
let isConnected = false;
const connectDatabase = async () => {
    try {
        if (isConnected) {
            logger_1.logger.info('Database already connected');
            return;
        }
        mongoose_1.default.set('strictQuery', true);
        await mongoose_1.default.connect(environment_1.config.database.uri, mongooseOptions);
        isConnected = true;
        logger_1.logger.info('✅ Connected to MongoDB successfully');
        mongoose_1.default.connection.on('error', (error) => {
            logger_1.logger.error('MongoDB connection error:', error);
            isConnected = false;
        });
        mongoose_1.default.connection.on('disconnected', () => {
            logger_1.logger.warn('MongoDB disconnected');
            isConnected = false;
        });
        mongoose_1.default.connection.on('reconnected', () => {
            logger_1.logger.info('MongoDB reconnected');
            isConnected = true;
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to MongoDB:', error);
        isConnected = false;
        throw error;
    }
};
exports.connectDatabase = connectDatabase;
const disconnectDatabase = async () => {
    try {
        if (!isConnected) {
            logger_1.logger.info('Database already disconnected');
            return;
        }
        await mongoose_1.default.disconnect();
        isConnected = false;
        logger_1.logger.info('✅ Disconnected from MongoDB successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to disconnect from MongoDB:', error);
        throw error;
    }
};
exports.disconnectDatabase = disconnectDatabase;
const getDatabaseStatus = () => {
    const connection = mongoose_1.default.connection;
    return {
        connected: isConnected && connection.readyState === 1,
        readyState: connection.readyState,
        host: connection.host,
        name: connection.name,
    };
};
exports.getDatabaseStatus = getDatabaseStatus;
const checkDatabaseHealth = async () => {
    try {
        if (!isConnected || mongoose_1.default.connection.readyState !== 1) {
            return {
                status: 'unhealthy',
                message: 'Database not connected',
                details: (0, exports.getDatabaseStatus)(),
            };
        }
        await mongoose_1.default.connection.db?.admin().ping();
        return {
            status: 'healthy',
            message: 'Database connection is healthy',
            details: (0, exports.getDatabaseStatus)(),
        };
    }
    catch (error) {
        logger_1.logger.error('Database health check failed:', error);
        return {
            status: 'unhealthy',
            message: 'Database health check failed',
            details: {
                error: error instanceof Error ? error.message : 'Unknown error',
                ...(0, exports.getDatabaseStatus)(),
            },
        };
    }
};
exports.checkDatabaseHealth = checkDatabaseHealth;
const clearDatabase = async () => {
    if (environment_1.config.env !== 'test') {
        throw new Error('clearDatabase can only be used in test environment');
    }
    try {
        const collections = await mongoose_1.default.connection.db?.collections() || [];
        for (const collection of collections) {
            await collection.deleteMany({});
        }
        logger_1.logger.info('Database cleared successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to clear database:', error);
        throw error;
    }
};
exports.clearDatabase = clearDatabase;
process.on('SIGINT', async () => {
    try {
        await (0, exports.disconnectDatabase)();
        process.exit(0);
    }
    catch (error) {
        logger_1.logger.error('Error during graceful shutdown:', error);
        process.exit(1);
    }
});
//# sourceMappingURL=database.js.map