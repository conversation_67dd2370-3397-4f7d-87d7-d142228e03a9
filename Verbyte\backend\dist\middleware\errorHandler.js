"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.errorHandler = exports.ServiceUnavailableError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.AppError = void 0;
const logger_1 = require("@/utils/logger");
const environment_1 = require("@/config/environment");
class AppError extends Error {
    constructor(message, statusCode = 500, code) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        this.code = code;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message, details) {
        super(message, 400, 'VALIDATION_ERROR');
        this.details = details;
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends AppError {
    constructor(message = 'Authentication required') {
        super(message, 401, 'AUTHENTICATION_ERROR');
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends AppError {
    constructor(message = 'Insufficient permissions') {
        super(message, 403, 'AUTHORIZATION_ERROR');
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends AppError {
    constructor(message = 'Resource not found') {
        super(message, 404, 'NOT_FOUND_ERROR');
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends AppError {
    constructor(message = 'Resource conflict') {
        super(message, 409, 'CONFLICT_ERROR');
    }
}
exports.ConflictError = ConflictError;
class RateLimitError extends AppError {
    constructor(message = 'Too many requests') {
        super(message, 429, 'RATE_LIMIT_ERROR');
    }
}
exports.RateLimitError = RateLimitError;
class ServiceUnavailableError extends AppError {
    constructor(message = 'Service temporarily unavailable') {
        super(message, 503, 'SERVICE_UNAVAILABLE_ERROR');
    }
}
exports.ServiceUnavailableError = ServiceUnavailableError;
const handleMongooseValidationError = (error) => {
    const errors = Object.values(error.errors).map((err) => ({
        field: err.path,
        message: err.message,
        value: err.value,
    }));
    return new ValidationError('Validation failed', errors);
};
const handleMongoDuplicateKeyError = (error) => {
    const field = Object.keys(error.keyValue)[0];
    const value = error.keyValue[field];
    return new ConflictError(`${field} '${value}' already exists`);
};
const handleMongoCastError = (error) => {
    return new ValidationError(`Invalid ${error.path}: ${error.value}`);
};
const handleJWTError = () => {
    return new AuthenticationError('Invalid token');
};
const handleJWTExpiredError = () => {
    return new AuthenticationError('Token expired');
};
const sendErrorDev = (err, res) => {
    res.status(err.statusCode).json({
        status: 'error',
        error: err,
        message: err.message,
        stack: err.stack,
        code: err.code,
    });
};
const sendErrorProd = (err, res) => {
    if (err.isOperational) {
        res.status(err.statusCode).json({
            status: 'error',
            message: err.message,
            code: err.code,
        });
    }
    else {
        logger_1.logger.error('Unexpected error:', err);
        res.status(500).json({
            status: 'error',
            message: 'Something went wrong!',
            code: 'INTERNAL_SERVER_ERROR',
        });
    }
};
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;
    logger_1.logger.error(`Error ${err.statusCode || 500}: ${err.message}`, {
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        stack: err.stack,
    });
    if (err.name === 'CastError') {
        error = handleMongoCastError(err);
    }
    if (err.code === 11000) {
        error = handleMongoDuplicateKeyError(err);
    }
    if (err.name === 'ValidationError') {
        error = handleMongooseValidationError(err);
    }
    if (err.name === 'JsonWebTokenError') {
        error = handleJWTError();
    }
    if (err.name === 'TokenExpiredError') {
        error = handleJWTExpiredError();
    }
    if (err.code === 'LIMIT_FILE_SIZE') {
        error = new ValidationError('File too large');
    }
    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        error = new ValidationError('Invalid file type');
    }
    if (!error.statusCode) {
        error.statusCode = 500;
        error.isOperational = false;
    }
    if (environment_1.config.env === 'development') {
        sendErrorDev(error, res);
    }
    else {
        sendErrorProd(error, res);
    }
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
process.on('uncaughtException', (err) => {
    logger_1.logger.error('Uncaught Exception:', err);
    process.exit(1);
});
//# sourceMappingURL=errorHandler.js.map