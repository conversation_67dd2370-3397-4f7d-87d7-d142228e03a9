"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logChatOperation = exports.logEmbeddingOperation = exports.logVectorOperation = exports.logDatabaseOperation = exports.logRequest = exports.logError = exports.loggerStream = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const environment_1 = require("@/config/environment");
const logsDir = path_1.default.dirname(environment_1.config.logging.file);
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    if (stack) {
        log += `\n${stack}`;
    }
    if (Object.keys(meta).length > 0) {
        log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    return log;
}));
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({
    format: 'HH:mm:ss',
}), winston_1.default.format.printf(({ timestamp, level, message, stack }) => {
    let log = `${timestamp} ${level}: ${message}`;
    if (stack) {
        log += `\n${stack}`;
    }
    return log;
}));
const transports = [];
if (environment_1.config.env === 'development') {
    transports.push(new winston_1.default.transports.Console({
        format: consoleFormat,
        level: environment_1.config.logging.level,
    }));
}
if (environment_1.config.env === 'production' || environment_1.config.env === 'test') {
    transports.push(new winston_1.default.transports.File({
        filename: environment_1.config.logging.file,
        format: logFormat,
        level: environment_1.config.logging.level,
        maxsize: 10 * 1024 * 1024,
        maxFiles: 5,
        tailable: true,
    }));
}
transports.push(new winston_1.default.transports.File({
    filename: path_1.default.join(logsDir, 'error.log'),
    format: logFormat,
    level: 'error',
    maxsize: 10 * 1024 * 1024,
    maxFiles: 5,
    tailable: true,
}));
exports.logger = winston_1.default.createLogger({
    level: environment_1.config.logging.level,
    format: logFormat,
    transports,
    exitOnError: false,
});
if (environment_1.config.env === 'production') {
    exports.logger.exceptions.handle(new winston_1.default.transports.File({
        filename: path_1.default.join(logsDir, 'exceptions.log'),
        format: logFormat,
    }));
    exports.logger.rejections.handle(new winston_1.default.transports.File({
        filename: path_1.default.join(logsDir, 'rejections.log'),
        format: logFormat,
    }));
}
exports.loggerStream = {
    write: (message) => {
        exports.logger.info(message.trim());
    },
};
const logError = (message, error, meta) => {
    exports.logger.error(message, {
        error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
        },
        ...meta,
    });
};
exports.logError = logError;
const logRequest = (req, res, responseTime) => {
    exports.logger.info('HTTP Request', {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        statusCode: res.statusCode,
        responseTime: responseTime ? `${responseTime}ms` : undefined,
    });
};
exports.logRequest = logRequest;
const logDatabaseOperation = (operation, collection, meta) => {
    exports.logger.debug('Database Operation', {
        operation,
        collection,
        ...meta,
    });
};
exports.logDatabaseOperation = logDatabaseOperation;
const logVectorOperation = (operation, collection, meta) => {
    exports.logger.debug('Vector Operation', {
        operation,
        collection,
        ...meta,
    });
};
exports.logVectorOperation = logVectorOperation;
const logEmbeddingOperation = (service, model, meta) => {
    exports.logger.debug('Embedding Operation', {
        service,
        model,
        ...meta,
    });
};
exports.logEmbeddingOperation = logEmbeddingOperation;
const logChatOperation = (model, tokensUsed, meta) => {
    exports.logger.info('Chat Operation', {
        model,
        tokensUsed,
        ...meta,
    });
};
exports.logChatOperation = logChatOperation;
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map