"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const joi_1 = __importDefault(require("joi"));
dotenv_1.default.config();
const envSchema = joi_1.default.object({
    NODE_ENV: joi_1.default.string().valid('development', 'production', 'test').default('development'),
    PORT: joi_1.default.number().default(8001),
    API_PREFIX: joi_1.default.string().default('/v1'),
    MONGODB_URI: joi_1.default.string().required(),
    MONGODB_TEST_URI: joi_1.default.string().when('NODE_ENV', {
        is: 'test',
        then: joi_1.default.required(),
        otherwise: joi_1.default.optional(),
    }),
    QDRANT_URL: joi_1.default.string().default('http://localhost:6333'),
    QDRANT_API_KEY: joi_1.default.string().optional(),
    QDRANT_COLLECTION_NAME: joi_1.default.string().default('verbyte_documents'),
    JWT_SECRET: joi_1.default.string().required(),
    JWT_EXPIRES_IN: joi_1.default.string().default('7d'),
    BCRYPT_ROUNDS: joi_1.default.number().default(12),
    MAX_FILE_SIZE: joi_1.default.number().default(10485760),
    UPLOAD_DIR: joi_1.default.string().default('uploads'),
    ALLOWED_FILE_TYPES: joi_1.default.string().default('pdf,docx,txt,md,json,csv,pptx,ppt,jpg,png,jpeg'),
    EMBEDDING_SERVICE: joi_1.default.string().valid('huggingface', 'openai', 'ollama').default('huggingface'),
    EMBEDDING_MODEL: joi_1.default.string().default('sentence-transformers/all-MiniLM-L6-v2'),
    EMBEDDING_DIMENSION: joi_1.default.number().default(384),
    HUGGINGFACE_API_KEY: joi_1.default.string().optional(),
    HUGGINGFACE_API_URL: joi_1.default.string().default('https://api-inference.huggingface.co/pipeline/feature-extraction'),
    OPENAI_API_KEY: joi_1.default.string().optional(),
    OPENAI_EMBEDDING_MODEL: joi_1.default.string().default('text-embedding-ada-002'),
    OPENAI_CHAT_MODEL: joi_1.default.string().default('gpt-3.5-turbo'),
    OLLAMA_BASE_URL: joi_1.default.string().default('http://localhost:11434'),
    OLLAMA_EMBEDDING_MODEL: joi_1.default.string().default('nomic-embed-text'),
    OLLAMA_CHAT_MODEL: joi_1.default.string().default('llama3.1'),
    LLM_SERVICE: joi_1.default.string().valid('openai', 'ollama').default('openai'),
    MAX_TOKENS: joi_1.default.number().default(512),
    TEMPERATURE: joi_1.default.number().min(0).max(2).default(0.1),
    CONTEXT_WINDOW: joi_1.default.number().default(3900),
    SIMILARITY_TOP_K: joi_1.default.number().default(5),
    SIMILARITY_THRESHOLD: joi_1.default.number().min(0).max(1).default(0.7),
    CHUNK_SIZE: joi_1.default.number().default(512),
    CHUNK_OVERLAP: joi_1.default.number().default(50),
    RERANK_ENABLED: joi_1.default.boolean().default(false),
    RERANK_MODEL: joi_1.default.string().default('cross-encoder/ms-marco-MiniLM-L-2-v2'),
    RERANK_TOP_N: joi_1.default.number().default(3),
    RATE_LIMIT_WINDOW_MS: joi_1.default.number().default(900000),
    RATE_LIMIT_MAX_REQUESTS: joi_1.default.number().default(100),
    LOG_LEVEL: joi_1.default.string().valid('error', 'warn', 'info', 'debug').default('info'),
    LOG_FILE: joi_1.default.string().default('logs/app.log'),
    CORS_ORIGIN: joi_1.default.string().default('http://localhost:3000'),
    CORS_CREDENTIALS: joi_1.default.boolean().default(true),
    REDIS_URL: joi_1.default.string().optional(),
    REDIS_PASSWORD: joi_1.default.string().optional(),
    SMTP_HOST: joi_1.default.string().optional(),
    SMTP_PORT: joi_1.default.number().optional(),
    SMTP_USER: joi_1.default.string().optional(),
    SMTP_PASSWORD: joi_1.default.string().optional(),
    FROM_EMAIL: joi_1.default.string().email().default('<EMAIL>'),
    SENTRY_DSN: joi_1.default.string().optional(),
    ANALYTICS_ENABLED: joi_1.default.boolean().default(false),
    DEBUG: joi_1.default.string().optional(),
}).unknown();
const { error, value: envVars } = envSchema.validate(process.env);
if (error) {
    throw new Error(`Config validation error: ${error.message}`);
}
exports.config = {
    env: envVars.NODE_ENV,
    port: envVars.PORT,
    apiPrefix: envVars.API_PREFIX,
    database: {
        uri: envVars.NODE_ENV === 'test' ? envVars.MONGODB_TEST_URI : envVars.MONGODB_URI,
        testUri: envVars.MONGODB_TEST_URI,
    },
    qdrant: {
        url: envVars.QDRANT_URL,
        apiKey: envVars.QDRANT_API_KEY,
        collectionName: envVars.QDRANT_COLLECTION_NAME,
    },
    auth: {
        jwtSecret: envVars.JWT_SECRET,
        jwtExpiresIn: envVars.JWT_EXPIRES_IN,
        bcryptRounds: envVars.BCRYPT_ROUNDS,
    },
    upload: {
        maxFileSize: envVars.MAX_FILE_SIZE,
        uploadDir: envVars.UPLOAD_DIR,
        allowedFileTypes: envVars.ALLOWED_FILE_TYPES.split(','),
    },
    embedding: {
        service: envVars.EMBEDDING_SERVICE,
        model: envVars.EMBEDDING_MODEL,
        dimension: envVars.EMBEDDING_DIMENSION,
    },
    huggingface: {
        apiKey: envVars.HUGGINGFACE_API_KEY,
        apiUrl: envVars.HUGGINGFACE_API_URL,
    },
    openai: {
        apiKey: envVars.OPENAI_API_KEY,
        embeddingModel: envVars.OPENAI_EMBEDDING_MODEL,
        chatModel: envVars.OPENAI_CHAT_MODEL,
    },
    ollama: {
        baseUrl: envVars.OLLAMA_BASE_URL,
        embeddingModel: envVars.OLLAMA_EMBEDDING_MODEL,
        chatModel: envVars.OLLAMA_CHAT_MODEL,
    },
    llm: {
        service: envVars.LLM_SERVICE,
        maxTokens: envVars.MAX_TOKENS,
        temperature: envVars.TEMPERATURE,
        contextWindow: envVars.CONTEXT_WINDOW,
    },
    rag: {
        similarityTopK: envVars.SIMILARITY_TOP_K,
        similarityThreshold: envVars.SIMILARITY_THRESHOLD,
        chunkSize: envVars.CHUNK_SIZE,
        chunkOverlap: envVars.CHUNK_OVERLAP,
        rerankEnabled: envVars.RERANK_ENABLED,
        rerankModel: envVars.RERANK_MODEL,
        rerankTopN: envVars.RERANK_TOP_N,
    },
    rateLimit: {
        windowMs: envVars.RATE_LIMIT_WINDOW_MS,
        maxRequests: envVars.RATE_LIMIT_MAX_REQUESTS,
    },
    logging: {
        level: envVars.LOG_LEVEL,
        file: envVars.LOG_FILE,
    },
    cors: {
        origin: envVars.CORS_ORIGIN,
        credentials: envVars.CORS_CREDENTIALS,
    },
    redis: {
        url: envVars.REDIS_URL,
        password: envVars.REDIS_PASSWORD,
    },
    email: {
        host: envVars.SMTP_HOST,
        port: envVars.SMTP_PORT,
        user: envVars.SMTP_USER,
        password: envVars.SMTP_PASSWORD,
        from: envVars.FROM_EMAIL,
    },
    monitoring: {
        sentryDsn: envVars.SENTRY_DSN,
        analyticsEnabled: envVars.ANALYTICS_ENABLED,
    },
    debug: envVars.DEBUG,
};
//# sourceMappingURL=environment.js.map