{"version": 3, "file": "powerPointProcessor.js", "sourceRoot": "", "sources": ["../../../src/services/processors/powerPointProcessor.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,iEAAyD;AACzD,2CAAwC;AACxC,+CAO0B;AAE1B,MAAa,mBAAmB;IAI9B,QAAQ,CAAC,QAAgB;QACvB,OAAO,QAAQ,KAAK,2EAA2E;YACxF,QAAQ,KAAK,+BAA+B;YAC5C,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC;YAC/B,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;IAC3C,CAAC;IAKD,OAAO;QACL,OAAO,uBAAY,CAAC,UAAU,CAAC;IACjC,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,QAAgB,EAAE,OAA2B;QACzD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;YAGxD,MAAM,MAAM,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAGzC,MAAM,SAAS,GAAG,IAAA,wCAAgB,GAAE,CAAC;YACrC,MAAM,aAAa,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YAGrF,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAGpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAG5D,MAAM,MAAM,GAAG,OAAO,EAAE,SAAS,CAAC,CAAC;gBACjC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC;gBACvE,SAAS,CAAC;YAGZ,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3C,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;YAEtC,MAAM,MAAM,GAAyB;gBACnC,IAAI;gBACJ,QAAQ;gBACR,MAAM;gBACN,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,IAAI;aACd,CAAC;YAEF,eAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,KAAK,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;YAC1F,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qCAAqC,CAAC;YACpG,eAAM,CAAC,KAAK,CAAC,iCAAiC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAEjE,OAAO;gBACL,IAAI,EAAE,EAAE;gBACR,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC;gBAC1D,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;gBACtC,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,IAAY;QACrC,OAAO,IAAI;aAER,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aAEpB,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;aAExB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;aAC3B,IAAI,EAAE,CAAC;IACZ,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,IAAY;QAC1D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAGpC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAuB;gBACnC,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACrC,QAAQ,EAAE,2EAA2E;gBACrF,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,uBAAY,CAAC,UAAU;gBAC7B,UAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,UAAU,EAAE,KAAK,CAAC,KAAK;aACxB,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,0CAA0C,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAKO,kBAAkB,CAAC,IAAY;QAErC,MAAM,eAAe,GAAG;YACtB,eAAe;YACf,cAAc;YACd,aAAa;SACd,CAAC;QAEF,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,OAAO,EAAE,CAAC;gBACZ,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC;QAErE,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAC/C,CAAC;IAKO,YAAY,CAAC,IAAY;QAC/B,MAAM,eAAe,GAAG;YACtB,aAAa;YACb,eAAe;YACf,eAAe;YACf,eAAe;YACf,gBAAgB;SACjB,CAAC;QAEF,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D,CAAC;IAKO,WAAW,CAAC,IAAY;QAC9B,MAAM,cAAc,GAAG;YACrB,WAAW;YACX,oBAAoB;YACpB,YAAY;SACb,CAAC;QAEF,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC;IAKO,gBAAgB,CAAC,IAAY;QACnC,MAAM,mBAAmB,GAAG;YAC1B,aAAa;YACb,cAAc;YACd,UAAU;YACV,gBAAgB;SACjB,CAAC;QAEF,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACjE,CAAC;IAKO,YAAY,CAAC,IAAY,EAAE,SAAiB,EAAE,OAAe;QACnE,MAAM,MAAM,GAAgB,EAAE,CAAC;QAG/B,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAE1B,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;gBACtC,IAAI,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;oBACrB,MAAM,CAAC,IAAI,CAAC;wBACV,EAAE,EAAE,aAAa,KAAK,GAAG,CAAC,EAAE;wBAC5B,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE;wBACtB,UAAU,EAAE,CAAC;wBACb,QAAQ,EAAE,SAAS,CAAC,MAAM;wBAC1B,QAAQ,EAAE;4BACR,MAAM,EAAE,YAAY;4BACpB,WAAW,EAAE,KAAK,GAAG,CAAC;4BACtB,UAAU,EAAE,MAAM,CAAC,MAAM;yBAC1B;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YAEN,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAErD,MAAM,CAAC,IAAI,CAAC;oBACV,EAAE,EAAE,aAAa,MAAM,CAAC,MAAM,EAAE;oBAChC,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,UAAU;oBACtB,QAAQ,EAAE,MAAM;oBAChB,QAAQ,EAAE;wBACR,MAAM,EAAE,YAAY;wBACpB,UAAU,EAAE,MAAM,CAAC,MAAM;qBAC1B;iBACF,CAAC,CAAC;gBAGH,UAAU,GAAG,MAAM,GAAG,OAAO,CAAC;gBAC9B,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM;oBAAE,MAAM;YACvC,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,aAAa,CAAC,IAAY;QAEhC,MAAM,UAAU,GAAG;YACjB,oBAAoB;YACpB,mBAAmB;YACnB,gBAAgB;YAChB,iBAAiB;SAClB,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACpC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAKO,UAAU,CAAC,IAAY;QAC7B,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IACzE,CAAC;IAKO,mBAAmB,CAAC,QAAgB;QAC1C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,OAAO;YACL,QAAQ,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACjC,YAAY,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACrC,QAAQ,EAAE,2EAA2E;YACrF,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,uBAAY,CAAC,UAAU;YAC7B,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,KAAK;YACpB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,UAAU,EAAE,KAAK,CAAC,KAAK;SACxB,CAAC;IACJ,CAAC;IAKO,mBAAmB,CAAC,QAAgB,EAAE,KAAa;QACzD,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO;YACL,GAAG,aAAa;YAChB,KAAK;SACN,CAAC;IACJ,CAAC;CACF;AAjTD,kDAiTC"}