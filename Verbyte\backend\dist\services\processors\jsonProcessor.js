"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JSONProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class JSONProcessor {
    supports(mimeType) {
        return mimeType === 'application/json' ||
            mimeType === 'text/json' ||
            mimeType.includes('json');
    }
    getType() {
        return document_1.DocumentType.JSON;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing JSON file: ${filePath}`);
            const jsonContent = fs_1.default.readFileSync(filePath, 'utf-8');
            const jsonData = JSON.parse(jsonContent);
            const metadata = await this.extractMetadata(filePath, jsonData, jsonContent);
            const text = this.jsonToText(jsonData, options?.preserveFormatting);
            const chunks = options?.chunkSize ?
                this.createChunks(text, jsonData, options.chunkSize, options.chunkOverlap || 0) :
                undefined;
            metadata.wordCount = this.countWords(text);
            metadata.characterCount = text.length;
            const result = {
                text,
                metadata,
                chunks,
                processingTime: Date.now() - startTime,
                success: true
            };
            logger_1.logger.debug(`JSON processing completed: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown JSON processing error';
            logger_1.logger.error(`JSON processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    jsonToText(data, preserveFormatting) {
        if (preserveFormatting) {
            return JSON.stringify(data, null, 2);
        }
        return this.extractTextFromValue(data, '');
    }
    extractTextFromValue(value, path) {
        const textParts = [];
        if (value === null || value === undefined) {
            return '';
        }
        if (typeof value === 'string') {
            textParts.push(value);
        }
        else if (typeof value === 'number' || typeof value === 'boolean') {
            textParts.push(String(value));
        }
        else if (Array.isArray(value)) {
            value.forEach((item, index) => {
                const itemPath = path ? `${path}[${index}]` : `[${index}]`;
                const itemText = this.extractTextFromValue(item, itemPath);
                if (itemText) {
                    textParts.push(itemText);
                }
            });
        }
        else if (typeof value === 'object') {
            Object.entries(value).forEach(([key, val]) => {
                const keyPath = path ? `${path}.${key}` : key;
                if (this.isMeaningfulKey(key)) {
                    textParts.push(`${key}:`);
                }
                const valText = this.extractTextFromValue(val, keyPath);
                if (valText) {
                    textParts.push(valText);
                }
            });
        }
        return textParts.join(' ').trim();
    }
    isMeaningfulKey(key) {
        const meaningfulKeys = [
            'title', 'name', 'description', 'content', 'text', 'message',
            'summary', 'body', 'comment', 'note', 'label', 'caption'
        ];
        return meaningfulKeys.some(meaningful => key.toLowerCase().includes(meaningful));
    }
    async extractMetadata(filePath, jsonData, jsonContent) {
        try {
            const stats = fs_1.default.statSync(filePath);
            const structure = Array.isArray(jsonData) ? 'array' :
                typeof jsonData === 'object' ? 'object' : 'mixed';
            const analysis = this.analyzeStructure(jsonData);
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: 'application/json',
                size: stats.size,
                type: document_1.DocumentType.JSON,
                structure,
                depth: analysis.depth,
                keyCount: analysis.keyCount,
                hasNestedObjects: analysis.hasNestedObjects,
                hasArrays: analysis.hasArrays,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime
            };
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract JSON metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    analyzeStructure(data, currentDepth = 0) {
        let maxDepth = currentDepth;
        let keyCount = 0;
        let hasNestedObjects = false;
        let hasArrays = false;
        if (Array.isArray(data)) {
            hasArrays = true;
            data.forEach(item => {
                const analysis = this.analyzeStructure(item, currentDepth + 1);
                maxDepth = Math.max(maxDepth, analysis.depth);
                keyCount += analysis.keyCount;
                hasNestedObjects = hasNestedObjects || analysis.hasNestedObjects;
                hasArrays = hasArrays || analysis.hasArrays;
            });
        }
        else if (typeof data === 'object' && data !== null) {
            if (currentDepth > 0) {
                hasNestedObjects = true;
            }
            Object.entries(data).forEach(([key, value]) => {
                keyCount++;
                const analysis = this.analyzeStructure(value, currentDepth + 1);
                maxDepth = Math.max(maxDepth, analysis.depth);
                keyCount += analysis.keyCount;
                hasNestedObjects = hasNestedObjects || analysis.hasNestedObjects;
                hasArrays = hasArrays || analysis.hasArrays;
            });
        }
        return {
            depth: maxDepth,
            keyCount,
            hasNestedObjects,
            hasArrays
        };
    }
    createChunks(text, jsonData, chunkSize, overlap) {
        const chunks = [];
        if (Array.isArray(jsonData)) {
            const itemsPerChunk = Math.max(1, Math.floor(chunkSize / 200));
            for (let i = 0; i < jsonData.length; i += itemsPerChunk) {
                const endIndex = Math.min(i + itemsPerChunk, jsonData.length);
                const chunkItems = jsonData.slice(i, endIndex);
                const chunkText = this.jsonToText(chunkItems);
                chunks.push({
                    id: `json_array_chunk_${chunks.length}`,
                    text: chunkText,
                    startIndex: i,
                    endIndex: endIndex,
                    metadata: {
                        source: 'json',
                        chunkIndex: chunks.length,
                        arrayStartIndex: i,
                        arrayEndIndex: endIndex - 1,
                        itemCount: chunkItems.length
                    }
                });
            }
        }
        else {
            let currentPos = 0;
            while (currentPos < text.length) {
                const endPos = Math.min(currentPos + chunkSize, text.length);
                const chunkText = text.substring(currentPos, endPos);
                chunks.push({
                    id: `json_chunk_${chunks.length}`,
                    text: chunkText,
                    startIndex: currentPos,
                    endIndex: endPos,
                    metadata: {
                        source: 'json',
                        chunkIndex: chunks.length
                    }
                });
                currentPos = endPos - overlap;
                if (currentPos >= text.length)
                    break;
            }
        }
        return chunks;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'application/json',
            size: stats.size,
            type: document_1.DocumentType.JSON,
            structure: 'mixed',
            depth: 0,
            keyCount: 0,
            hasNestedObjects: false,
            hasArrays: false,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.JSONProcessor = JSONProcessor;
//# sourceMappingURL=jsonProcessor.js.map