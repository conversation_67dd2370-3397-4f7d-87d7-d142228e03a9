{"version": 3, "file": "documentProcessingService.js", "sourceRoot": "", "sources": ["../../src/services/documentProcessingService.ts"], "names": [], "mappings": ";;;AAAA,2CAAwC;AACxC,mDAAgD;AAChD,2DAAgE;AAChE,4CAA0E;AAM1E,MAAa,mCAAmC;IAI9C;QACE,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;QACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,6CAAyB,EAAE,CAAC;IAC3D,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAC3B,QAAgB,EAChB,OAA2B;QAE3B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,4CAA4C,QAAQ,EAAE,CAAC,CAAC;YAGpE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAC;YACnD,CAAC;YAGD,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CACnC,QAAQ,EACR,qBAAY,CAAC,UAAU,EACvB,yBAAgB,CAAC,eAAe,CACjC,CAAC;YAGF,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YAG3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAElF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBAEnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAGhD,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CACnC,QAAQ,EACR,qBAAY,CAAC,SAAS,EACtB,yBAAgB,CAAC,SAAS,CAC3B,CAAC;gBAGF,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAE5D,eAAM,CAAC,IAAI,CAAC,0DAA0D,QAAQ,EAAE,CAAC,CAAC;YACpF,CAAC;iBAAM,CAAC;gBAEN,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAC/B,QAAQ,EACR,MAAM,CAAC,KAAK,IAAI,4BAA4B,EAC5C,mBAAmB,CACpB,CAAC;gBAEF,eAAM,CAAC,KAAK,CAAC,0CAA0C,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YACnF,CAAC;YAED,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B,CAAC;YAGzF,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;YAE9E,eAAM,CAAC,KAAK,CAAC,yCAAyC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,SAAmB,EACnB,OAA2B;QAE3B,eAAM,CAAC,IAAI,CAAC,iCAAiC,SAAS,CAAC,MAAM,UAAU,CAAC,CAAC;QAEzE,MAAM,OAAO,GAAyD,EAAE,CAAC;QAEzE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBACrE,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,QAAQ,YAAY,EAAE,KAAK,CAAC,CAAC;gBAGtE,OAAO,CAAC,IAAI,CAAC;oBACX,QAAQ;oBACR,MAAM,EAAE;wBACN,IAAI,EAAE,EAAE;wBACR,QAAQ,EAAE;4BACR,QAAQ,EAAE,SAAS;4BACnB,YAAY,EAAE,SAAS;4BACvB,QAAQ,EAAE,SAAS;4BACnB,IAAI,EAAE,CAAC;4BACP,IAAI,EAAE,MAAa;4BACnB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;yBAChE;wBACD,cAAc,EAAE,CAAC;wBACjB,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;qBAChE;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,+BAA+B,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,aAAa,CAAC,CAAC;QAC1H,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,OAA2B;QACrD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YAEpE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,eAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;gBAC9C,OAAO,CAAC,CAAC;YACX,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,SAAS,cAAc,CAAC,MAAM,6BAA6B,CAAC,CAAC;YAEzE,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtE,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAEnD,OAAO,cAAc,CAAC,MAAM,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,eAAe,CAAC,QAAgB;QAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAKD,sBAAsB;QACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,CAAC;IACzD,CAAC;IAKO,KAAK,CAAC,oBAAoB,CAChC,MAAe,EACf,MAA4B;QAE5B,IAAI,CAAC;YAEH,MAAM,CAAC,QAAQ,GAAG;gBAChB,GAAG,MAAM,CAAC,QAAQ;gBAClB,aAAa,EAAE,IAAI;gBACnB,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK;gBAC5B,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS;gBACpC,cAAc,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc;gBAC9C,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;gBAClC,YAAY,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;gBAClC,cAAc,EAAE,MAAM,CAAC,cAAc;gBACrC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC;aACnC,CAAC;YAEF,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,gCAAgC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;QAEzE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB;QAOtB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;YAExD,MAAM,cAAc,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC5F,MAAM,oBAAoB,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC;YAChE,MAAM,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC;YACzD,MAAM,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAG3D,MAAM,qBAAqB,GAAG,CAAC,CAAC;YAEhC,OAAO;gBACL,cAAc;gBACd,oBAAoB;gBACpB,gBAAgB;gBAChB,iBAAiB;gBACjB,qBAAqB;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,OAA2B;QACjE,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;YAGhD,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,CACnC,QAAQ,EACR,qBAAY,CAAC,QAAQ,EACrB,yBAAgB,CAAC,OAAO,CACzB,CAAC;YAGF,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAG1D,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;YAC/D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,kBAAkB;QAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;IAC5C,CAAC;IAKD,qBAAqB,CAAC,MAAW;QAC/B,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC9C,CAAC;CACF;AAnQD,kFAmQC"}