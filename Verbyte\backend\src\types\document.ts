/**
 * Document processing types and interfaces
 */

// Supported document types
export enum DocumentType {
  PDF = 'pdf',
  DOCX = 'docx',
  CSV = 'csv',
  MARKDOWN = 'markdown',
  JSON = 'json',
  IMAGE = 'image',
  POWERPOINT = 'powerpoint',
  TEXT = 'text'
}

// Document processing status
export enum ProcessingStage {
  PENDING = 'pending',
  EXTRACTING_TEXT = 'extracting_text',
  PROCESSING_METADATA = 'processing_metadata',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Base document metadata interface
export interface DocumentMetadata {
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  type: DocumentType;
  pages?: number;
  wordCount?: number;
  characterCount?: number;
  language?: string;
  encoding?: string;
  createdAt?: Date;
  modifiedAt?: Date;
  author?: string;
  title?: string;
  subject?: string;
  keywords?: string[];
  [key: string]: any;
}

// Text extraction result
export interface TextExtractionResult {
  text: string;
  metadata: DocumentMetadata;
  chunks?: TextChunk[];
  processingTime: number;
  success: boolean;
  error?: string;
}

// Text chunk for processing
export interface TextChunk {
  id: string;
  text: string;
  startIndex: number;
  endIndex: number;
  page?: number;
  section?: string;
  metadata?: Record<string, any>;
}

// PDF-specific metadata
export interface PDFMetadata extends DocumentMetadata {
  pdfVersion?: string;
  isEncrypted?: boolean;
  hasImages?: boolean;
  hasAnnotations?: boolean;
  producer?: string;
  creator?: string;
}

// DOCX-specific metadata
export interface DOCXMetadata extends DocumentMetadata {
  hasImages?: boolean;
  hasHeaders?: boolean;
  hasFooters?: boolean;
  hasTables?: boolean;
  styleCount?: number;
}

// CSV-specific metadata
export interface CSVMetadata extends DocumentMetadata {
  delimiter?: string;
  hasHeaders?: boolean;
  rowCount?: number;
  columnCount?: number;
  columns?: string[];
  encoding?: string;
}

// Image-specific metadata
export interface ImageMetadata extends DocumentMetadata {
  width?: number;
  height?: number;
  format?: string;
  colorSpace?: string;
  hasText?: boolean;
  ocrLanguage?: string;
  confidence?: number;
}

// PowerPoint-specific metadata
export interface PowerPointMetadata extends DocumentMetadata {
  slideCount?: number;
  hasImages?: boolean;
  hasNotes?: boolean;
  hasAnimations?: boolean;
  template?: string;
}

// JSON-specific metadata
export interface JSONMetadata extends DocumentMetadata {
  structure?: 'object' | 'array' | 'mixed';
  depth?: number;
  keyCount?: number;
  hasNestedObjects?: boolean;
  hasArrays?: boolean;
}

// Markdown-specific metadata
export interface MarkdownMetadata extends DocumentMetadata {
  hasHeaders?: boolean;
  hasLinks?: boolean;
  hasImages?: boolean;
  hasTables?: boolean;
  hasCodeBlocks?: boolean;
  frontMatter?: Record<string, any>;
}

// Processing options for different document types
export interface ProcessingOptions {
  extractImages?: boolean;
  preserveFormatting?: boolean;
  includeMetadata?: boolean;
  chunkSize?: number;
  chunkOverlap?: number;
  ocrLanguage?: string;
  csvDelimiter?: string;
  csvHasHeaders?: boolean;
  maxFileSize?: number;
  timeout?: number;
}

// Document processing configuration
export interface DocumentProcessorConfig {
  maxFileSize: number;
  supportedTypes: DocumentType[];
  defaultOptions: ProcessingOptions;
  ocrEnabled: boolean;
  tempDirectory: string;
  cleanupAfterProcessing: boolean;
}

// Error types for document processing
export enum DocumentProcessingError {
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  CORRUPTED_FILE = 'CORRUPTED_FILE',
  EXTRACTION_FAILED = 'EXTRACTION_FAILED',
  OCR_FAILED = 'OCR_FAILED',
  TIMEOUT = 'TIMEOUT',
  INSUFFICIENT_MEMORY = 'INSUFFICIENT_MEMORY',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// Document processing error details
export interface ProcessingError {
  code: DocumentProcessingError;
  message: string;
  details?: string;
  stage?: ProcessingStage;
  timestamp: Date;
}

// Document processor interface
export interface IDocumentProcessor {
  process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
  supports(mimeType: string): boolean;
  getType(): DocumentType;
}

// Batch processing result
export interface BatchProcessingResult {
  totalFiles: number;
  successfulFiles: number;
  failedFiles: number;
  results: TextExtractionResult[];
  errors: ProcessingError[];
  processingTime: number;
}
