"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PDFProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const pdfjsLib = __importStar(require("pdfjs-dist"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class PDFProcessor {
    constructor() {
        try {
            pdfjsLib.GlobalWorkerOptions.workerSrc = require.resolve('pdfjs-dist/build/pdf.worker.js');
        }
        catch (error) {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'pdfjs-dist/build/pdf.worker.js';
        }
    }
    supports(mimeType) {
        return mimeType === 'application/pdf' || mimeType.includes('pdf');
    }
    getType() {
        return document_1.DocumentType.PDF;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing PDF file: ${filePath}`);
            const data = new Uint8Array(fs_1.default.readFileSync(filePath));
            const pdfDocument = await pdfjsLib.getDocument({
                data,
                verbosity: 0
            }).promise;
            const metadata = await this.extractMetadata(pdfDocument, filePath);
            const { text, chunks } = await this.extractTextFromPages(pdfDocument, options);
            metadata.wordCount = this.countWords(text);
            metadata.characterCount = text.length;
            const result = {
                text,
                metadata,
                chunks: options?.chunkSize ? chunks : undefined,
                processingTime: Date.now() - startTime,
                success: true
            };
            logger_1.logger.debug(`PDF processing completed: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown PDF processing error';
            logger_1.logger.error(`PDF processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    async extractMetadata(pdfDocument, filePath) {
        try {
            const info = await pdfDocument.getMetadata();
            const stats = fs_1.default.statSync(filePath);
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: 'application/pdf',
                size: stats.size,
                type: document_1.DocumentType.PDF,
                pages: pdfDocument.numPages,
                pdfVersion: info.info?.PDFFormatVersion || 'unknown',
                isEncrypted: pdfDocument.isEncrypted || false,
                title: info.info?.Title || undefined,
                author: info.info?.Author || undefined,
                subject: info.info?.Subject || undefined,
                keywords: info.info?.Keywords ? info.info.Keywords.split(',').map((k) => k.trim()) : undefined,
                creator: info.info?.Creator || undefined,
                producer: info.info?.Producer || undefined,
                createdAt: info.info?.CreationDate ? new Date(info.info.CreationDate) : undefined,
                modifiedAt: info.info?.ModDate ? new Date(info.info.ModDate) : undefined
            };
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract PDF metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    async extractTextFromPages(pdfDocument, options) {
        const textParts = [];
        const chunks = [];
        let currentIndex = 0;
        for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
            try {
                const page = await pdfDocument.getPage(pageNum);
                const textContent = await page.getTextContent();
                const pageText = textContent.items
                    .map((item) => item.str)
                    .join(' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                if (pageText) {
                    textParts.push(pageText);
                    if (options?.chunkSize) {
                        const pageChunks = this.createChunks(pageText, currentIndex, pageNum, options.chunkSize, options.chunkOverlap || 0);
                        chunks.push(...pageChunks);
                        currentIndex += pageText.length + 1;
                    }
                }
                logger_1.logger.debug(`Extracted text from page ${pageNum}/${pdfDocument.numPages}: ${pageText.length} characters`);
            }
            catch (error) {
                logger_1.logger.warn(`Failed to extract text from page ${pageNum}:`, error);
            }
        }
        const fullText = textParts.join('\n\n');
        return { text: fullText, chunks };
    }
    createChunks(text, startIndex, page, chunkSize, overlap) {
        const chunks = [];
        let currentPos = 0;
        while (currentPos < text.length) {
            const endPos = Math.min(currentPos + chunkSize, text.length);
            const chunkText = text.substring(currentPos, endPos);
            chunks.push({
                id: `pdf_page_${page}_chunk_${chunks.length}`,
                text: chunkText,
                startIndex: startIndex + currentPos,
                endIndex: startIndex + endPos,
                page,
                metadata: {
                    source: 'pdf',
                    page,
                    chunkIndex: chunks.length
                }
            });
            currentPos = endPos - overlap;
            if (currentPos >= text.length)
                break;
        }
        return chunks;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'application/pdf',
            size: stats.size,
            type: document_1.DocumentType.PDF,
            pages: 0,
            pdfVersion: 'unknown',
            isEncrypted: false
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.PDFProcessor = PDFProcessor;
//# sourceMappingURL=pdfProcessor.js.map