"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const environment_1 = require("@/config/environment");
const database_1 = require("@/config/database");
const qdrant_1 = require("@/config/qdrant");
const fileCleanup_1 = require("@/utils/fileCleanup");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const notFoundHandler_1 = require("@/middleware/notFoundHandler");
const auth_1 = require("@/routes/auth");
const health_1 = require("@/routes/health");
const ingest_1 = require("@/routes/ingest");
const chat_1 = require("@/routes/chat");
const chunks_1 = require("@/routes/chunks");
const embeddings_1 = require("@/routes/embeddings");
const app = (0, express_1.default)();
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: environment_1.config.cors.origin,
    credentials: environment_1.config.cors.credentials,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: environment_1.config.rateLimit.windowMs,
    max: environment_1.config.rateLimit.maxRequests,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
app.use((0, morgan_1.default)('combined', {
    stream: {
        write: (message) => logger_1.logger.info(message.trim()),
    },
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use(`${environment_1.config.apiPrefix}/auth`, auth_1.authRoutes);
app.use(`${environment_1.config.apiPrefix}/health`, health_1.healthRoutes);
app.use(`${environment_1.config.apiPrefix}/ingest`, ingest_1.ingestRoutes);
app.use(`${environment_1.config.apiPrefix}/chat`, chat_1.chatRoutes);
app.use(`${environment_1.config.apiPrefix}/chunks`, chunks_1.chunksRoutes);
app.use(`${environment_1.config.apiPrefix}/embeddings`, embeddings_1.embeddingsRoutes);
app.use(notFoundHandler_1.notFoundHandler);
app.use(errorHandler_1.errorHandler);
const gracefulShutdown = (signal) => {
    logger_1.logger.info(`Received ${signal}. Starting graceful shutdown...`);
    fileCleanup_1.fileCleanupService.stopCleanupJob();
    process.exit(0);
};
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
const startServer = async () => {
    try {
        await (0, database_1.connectDatabase)();
        await (0, qdrant_1.connectQdrant)();
        fileCleanup_1.fileCleanupService.startCleanupJob();
        const server = app.listen(environment_1.config.port, () => {
            logger_1.logger.info(`🚀 Verbyte server running on port ${environment_1.config.port}`);
            logger_1.logger.info(`📚 API documentation available at http://localhost:${environment_1.config.port}${environment_1.config.apiPrefix}/docs`);
            logger_1.logger.info(`🏥 Health check available at http://localhost:${environment_1.config.port}${environment_1.config.apiPrefix}/health`);
            logger_1.logger.info(`🧹 File cleanup service started`);
        });
        server.on('error', (error) => {
            logger_1.logger.error('Server error:', error);
            process.exit(1);
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to start server:', error);
        process.exit(1);
    }
};
startServer();
exports.default = app;
//# sourceMappingURL=index.js.map