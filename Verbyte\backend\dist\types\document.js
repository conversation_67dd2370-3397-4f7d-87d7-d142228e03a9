"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentProcessingError = exports.ProcessingStage = exports.DocumentType = void 0;
var DocumentType;
(function (DocumentType) {
    DocumentType["PDF"] = "pdf";
    DocumentType["DOCX"] = "docx";
    DocumentType["CSV"] = "csv";
    DocumentType["MARKDOWN"] = "markdown";
    DocumentType["JSON"] = "json";
    DocumentType["IMAGE"] = "image";
    DocumentType["POWERPOINT"] = "powerpoint";
    DocumentType["TEXT"] = "text";
})(DocumentType || (exports.DocumentType = DocumentType = {}));
var ProcessingStage;
(function (ProcessingStage) {
    ProcessingStage["PENDING"] = "pending";
    ProcessingStage["EXTRACTING_TEXT"] = "extracting_text";
    ProcessingStage["PROCESSING_METADATA"] = "processing_metadata";
    ProcessingStage["COMPLETED"] = "completed";
    ProcessingStage["FAILED"] = "failed";
})(ProcessingStage || (exports.ProcessingStage = ProcessingStage = {}));
var DocumentProcessingError;
(function (DocumentProcessingError) {
    DocumentProcessingError["UNSUPPORTED_FORMAT"] = "UNSUPPORTED_FORMAT";
    DocumentProcessingError["FILE_TOO_LARGE"] = "FILE_TOO_LARGE";
    DocumentProcessingError["CORRUPTED_FILE"] = "CORRUPTED_FILE";
    DocumentProcessingError["EXTRACTION_FAILED"] = "EXTRACTION_FAILED";
    DocumentProcessingError["OCR_FAILED"] = "OCR_FAILED";
    DocumentProcessingError["TIMEOUT"] = "TIMEOUT";
    DocumentProcessingError["INSUFFICIENT_MEMORY"] = "INSUFFICIENT_MEMORY";
    DocumentProcessingError["PERMISSION_DENIED"] = "PERMISSION_DENIED";
    DocumentProcessingError["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
})(DocumentProcessingError || (exports.DocumentProcessingError = DocumentProcessingError = {}));
//# sourceMappingURL=document.js.map