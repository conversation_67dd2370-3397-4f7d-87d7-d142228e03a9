import multer from 'multer';
import { Request } from 'express';
export declare const uploadToDisk: multer.Multer;
export declare const uploadToMemory: multer.Multer;
export declare const uploadSingleFile: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadMultipleFiles: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadSingleFileToMemory: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const uploadMultipleFilesToMemory: import("express").RequestHandler<import("express-serve-static-core").ParamsDictionary, any, any, import("qs").ParsedQs, Record<string, any>>;
export declare const validateUploadedFile: (req: Request, res: any, next: any) => void;
export declare const cleanupTempFiles: (req: Request, res: any, next: any) => void;
declare global {
    namespace Express {
        interface Request {
            fileMetadata?: {
                originalName?: string;
                generatedName?: string;
                uploadId?: string;
            };
        }
    }
}
//# sourceMappingURL=upload.d.ts.map