"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chatRoutes = void 0;
const express_1 = require("express");
const router = (0, express_1.Router)();
exports.chatRoutes = router;
router.post('/completions', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Chat endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
router.get('/history', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Chat endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
router.delete('/history', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Chat endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
//# sourceMappingURL=chat.js.map