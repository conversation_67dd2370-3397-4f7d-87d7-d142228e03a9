export declare const config: {
    env: any;
    port: any;
    apiPrefix: any;
    database: {
        uri: any;
        testUri: any;
    };
    qdrant: {
        url: any;
        apiKey: any;
        collectionName: any;
    };
    auth: {
        jwtSecret: any;
        jwtExpiresIn: any;
        bcryptRounds: any;
    };
    upload: {
        maxFileSize: any;
        uploadDir: any;
        allowedFileTypes: any;
    };
    embedding: {
        service: any;
        model: any;
        dimension: any;
    };
    huggingface: {
        apiKey: any;
        apiUrl: any;
    };
    openai: {
        apiKey: any;
        embeddingModel: any;
        chatModel: any;
    };
    ollama: {
        baseUrl: any;
        embeddingModel: any;
        chatModel: any;
    };
    llm: {
        service: any;
        maxTokens: any;
        temperature: any;
        contextWindow: any;
    };
    rag: {
        similarityTopK: any;
        similarityThreshold: any;
        chunkSize: any;
        chunkOverlap: any;
        rerankEnabled: any;
        rerankModel: any;
        rerankTopN: any;
    };
    rateLimit: {
        windowMs: any;
        maxRequests: any;
    };
    logging: {
        level: any;
        file: any;
    };
    cors: {
        origin: any;
        credentials: any;
    };
    redis: {
        url: any;
        password: any;
    };
    email: {
        host: any;
        port: any;
        user: any;
        password: any;
        from: any;
    };
    monitoring: {
        sentryDsn: any;
        analyticsEnabled: any;
    };
    debug: any;
};
//# sourceMappingURL=environment.d.ts.map