import { DocumentType, TextExtractionResult, ProcessingOptions, IDocumentProcessor } from '@/types/document';
export declare class TextProcessor implements IDocumentProcessor {
    supports(mimeType: string): boolean;
    getType(): DocumentType;
    process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    private readTextFile;
    private extractMetadata;
    private detectLanguage;
    private createChunks;
    private countWords;
    private createBasicMetadata;
    private createErrorMetadata;
}
//# sourceMappingURL=textProcessor.d.ts.map