{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAK1D,qBAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,CAAC,EAAE,MAAM,CAAC;gBAET,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,IAAI,CAAC,EAAE,MAAM;CAQrE;AAGD,qBAAa,eAAgB,SAAQ,QAAQ;IACpC,OAAO,EAAE,GAAG,CAAC;gBAER,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAI3C;AAGD,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAkC;CAGxD;AAGD,qBAAa,kBAAmB,SAAQ,QAAQ;gBAClC,OAAO,GAAE,MAAmC;CAGzD;AAGD,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA6B;CAGnD;AAGD,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA4B;CAGlD;AAGD,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA4B;CAGlD;AAGD,qBAAa,uBAAwB,SAAQ,QAAQ;gBACvC,OAAO,GAAE,MAA0C;CAGhE;AAqED,eAAO,MAAM,YAAY,GACvB,KAAK,GAAG,EACR,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IA4DF,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC"}