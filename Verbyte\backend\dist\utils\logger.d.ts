import winston from 'winston';
export declare const logger: winston.Logger;
export declare const loggerStream: {
    write: (message: string) => void;
};
export declare const logError: (message: string, error: Error, meta?: any) => void;
export declare const logRequest: (req: any, res: any, responseTime?: number) => void;
export declare const logDatabaseOperation: (operation: string, collection: string, meta?: any) => void;
export declare const logVectorOperation: (operation: string, collection: string, meta?: any) => void;
export declare const logEmbeddingOperation: (service: string, model: string, meta?: any) => void;
export declare const logChatOperation: (model: string, tokensUsed?: number, meta?: any) => void;
export default logger;
//# sourceMappingURL=logger.d.ts.map