"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthRoutes = void 0;
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const database_1 = require("@/config/database");
const qdrant_1 = require("@/config/qdrant");
const logger_1 = require("@/utils/logger");
const router = (0, express_1.Router)();
exports.healthRoutes = router;
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const timestamp = new Date().toISOString();
    res.status(200).json({
        status: 'healthy',
        timestamp,
        service: 'verbyte-api',
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
    });
}));
router.get('/detailed', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const timestamp = new Date().toISOString();
    const databaseHealth = await (0, database_1.checkDatabaseHealth)();
    const qdrantHealth = await (0, qdrant_1.checkQdrantHealth)();
    const isHealthy = databaseHealth.status === 'healthy' && qdrantHealth.status === 'healthy';
    const overallStatus = isHealthy ? 'healthy' : 'unhealthy';
    const statusCode = isHealthy ? 200 : 503;
    const healthData = {
        status: overallStatus,
        timestamp,
        service: 'verbyte-api',
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        checks: {
            database: databaseHealth,
            qdrant: qdrantHealth,
        },
        system: {
            memory: {
                used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
                external: Math.round(process.memoryUsage().external / 1024 / 1024),
            },
            cpu: {
                usage: process.cpuUsage(),
            },
            node: {
                version: process.version,
                platform: process.platform,
                arch: process.arch,
            },
        },
    };
    if (!isHealthy) {
        logger_1.logger.warn('Health check failed', healthData);
    }
    res.status(statusCode).json(healthData);
}));
router.get('/ready', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const databaseHealth = await (0, database_1.checkDatabaseHealth)();
    const qdrantHealth = await (0, qdrant_1.checkQdrantHealth)();
    const isReady = databaseHealth.status === 'healthy' && qdrantHealth.status === 'healthy';
    if (isReady) {
        res.status(200).json({
            status: 'ready',
            timestamp: new Date().toISOString(),
            message: 'Service is ready to accept requests',
        });
    }
    else {
        res.status(503).json({
            status: 'not ready',
            timestamp: new Date().toISOString(),
            message: 'Service is not ready to accept requests',
            checks: {
                database: databaseHealth.status,
                qdrant: qdrantHealth.status,
            },
        });
    }
}));
router.get('/live', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.status(200).json({
        status: 'alive',
        timestamp: new Date().toISOString(),
        message: 'Service is alive',
        uptime: process.uptime(),
    });
}));
//# sourceMappingURL=health.js.map