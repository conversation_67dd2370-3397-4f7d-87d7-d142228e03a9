export declare const connectDatabase: () => Promise<void>;
export declare const disconnectDatabase: () => Promise<void>;
export declare const getDatabaseStatus: () => {
    connected: boolean;
    readyState: number;
    host?: string;
    name?: string;
};
export declare const checkDatabaseHealth: () => Promise<{
    status: "healthy" | "unhealthy";
    message: string;
    details?: any;
}>;
export declare const clearDatabase: () => Promise<void>;
//# sourceMappingURL=database.d.ts.map