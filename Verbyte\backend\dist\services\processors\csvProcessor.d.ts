import { DocumentType, TextExtractionResult, ProcessingOptions, IDocumentProcessor } from '@/types/document';
export declare class CSVProcessor implements IDocumentProcessor {
    supports(mimeType: string): boolean;
    getType(): DocumentType;
    process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    private extractMetadata;
    private convertToText;
    private createChunks;
    private detectDelimiter;
    private countWords;
    private createBasicMetadata;
    private createErrorMetadata;
}
//# sourceMappingURL=csvProcessor.d.ts.map