{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,2CAAwC;AACxC,sDAA8C;AAG9C,MAAa,QAAS,SAAQ,KAAK;IAKjC,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,IAAa;QAClE,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAbD,4BAaC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAG3C,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AAPD,0CAOC;AAGD,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,yBAAyB;QACrD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,sBAAsB,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,kDAIC;AAGD,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,0BAA0B;QACtD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC7C,CAAC;CACF;AAJD,gDAIC;AAGD,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,oBAAoB;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,iBAAiB,CAAC,CAAC;IACzC,CAAC;CACF;AAJD,sCAIC;AAGD,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC;IACxC,CAAC;CACF;AAJD,sCAIC;AAGD,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC;IAC1C,CAAC;CACF;AAJD,wCAIC;AAGD,MAAa,uBAAwB,SAAQ,QAAQ;IACnD,YAAY,UAAkB,iCAAiC;QAC7D,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,2BAA2B,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,0DAIC;AAGD,MAAM,6BAA6B,GAAG,CAAC,KAAU,EAAY,EAAE;IAC7D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;QAC5D,KAAK,EAAE,GAAG,CAAC,IAAI;QACf,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,KAAK,EAAE,GAAG,CAAC,KAAK;KACjB,CAAC,CAAC,CAAC;IAEJ,OAAO,IAAI,eAAe,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;AAC1D,CAAC,CAAC;AAGF,MAAM,4BAA4B,GAAG,CAAC,KAAU,EAAY,EAAE;IAC5D,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAEpC,OAAO,IAAI,aAAa,CAAC,GAAG,KAAK,KAAK,KAAK,kBAAkB,CAAC,CAAC;AACjE,CAAC,CAAC;AAGF,MAAM,oBAAoB,GAAG,CAAC,KAAU,EAAY,EAAE;IACpD,OAAO,IAAI,eAAe,CAAC,WAAW,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;AACtE,CAAC,CAAC;AAGF,MAAM,cAAc,GAAG,GAAa,EAAE;IACpC,OAAO,IAAI,mBAAmB,CAAC,eAAe,CAAC,CAAC;AAClD,CAAC,CAAC;AAGF,MAAM,qBAAqB,GAAG,GAAa,EAAE;IAC3C,OAAO,IAAI,mBAAmB,CAAC,eAAe,CAAC,CAAC;AAClD,CAAC,CAAC;AAGF,MAAM,YAAY,GAAG,CAAC,GAAa,EAAE,GAAa,EAAE,EAAE;IACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC9B,MAAM,EAAE,OAAO;QACf,KAAK,EAAE,GAAG;QACV,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC,CAAC;AACL,CAAC,CAAC;AAGF,MAAM,aAAa,GAAG,CAAC,GAAa,EAAE,GAAa,EAAE,EAAE;IAErD,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;QACtB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;YAC9B,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,IAAI,EAAE,GAAG,CAAC,IAAI;SACf,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QAEN,eAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;QAEvC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,uBAAuB;YAChC,IAAI,EAAE,uBAAuB;SAC9B,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAGK,MAAM,YAAY,GAAG,CAC1B,GAAQ,EACR,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAG5B,eAAM,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,UAAU,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,EAAE,EAAE;QAC7D,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,KAAK,EAAE,GAAG,CAAC,KAAK;KACjB,CAAC,CAAC;IAGH,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC7B,KAAK,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;QACvB,KAAK,GAAG,4BAA4B,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACnC,KAAK,GAAG,6BAA6B,CAAC,GAAG,CAAC,CAAC;IAC7C,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,KAAK,GAAG,cAAc,EAAE,CAAC;IAC3B,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,KAAK,GAAG,qBAAqB,EAAE,CAAC;IAClC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACnC,KAAK,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAChD,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;QACzC,KAAK,GAAG,IAAI,eAAe,CAAC,mBAAmB,CAAC,CAAC;IACnD,CAAC;IAGD,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;QACtB,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;QACvB,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;IAC9B,CAAC;IAGD,IAAI,oBAAM,CAAC,GAAG,KAAK,aAAa,EAAE,CAAC;QACjC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC3B,CAAC;SAAM,CAAC;QACN,aAAa,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC,CAAC;AAjEW,QAAA,YAAY,gBAiEvB;AAGK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAGF,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAW,EAAE,OAAqB,EAAE,EAAE;IACtE,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAEpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAGH,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,GAAU,EAAE,EAAE;IAC7C,eAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;IAEzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}