{"version": 3, "file": "fileCleanup.js", "sourceRoot": "", "sources": ["../../src/utils/fileCleanup.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,gDAAwB;AACxB,0DAA6B;AAC7B,sDAA8C;AAC9C,2CAAwC;AACxC,4DAAyD;AAEzD,MAAa,kBAAkB;IAI7B;QAFQ,eAAU,GAA8B,IAAI,CAAC;QAGnD,IAAI,CAAC,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;IAC3C,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,iBAAyB,EAAE;QAChD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,oBAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEpE,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,eAAM,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;gBAClE,OAAO,CAAC,CAAC;YACX,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAClE,MAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC5C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAGpC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;wBACxB,SAAS;oBACX,CAAC;oBAGD,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,UAAU,EAAE,CAAC;wBACvC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBACxB,YAAY,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;oBAChD,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,+BAA+B,cAAc,QAAQ,CAAC,CAAC;YAC/F,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,oBAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEpE,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,eAAM,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAC;gBAChF,OAAO,CAAC,CAAC;YACX,CAAC;YAED,MAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC5C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAGpC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;wBACxB,SAAS;oBACX,CAAC;oBAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBAChE,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CACtC,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CACvD,CAAC;oBAEF,IAAI,CAAC,SAAS,EAAE,CAAC;wBAEf,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBACxB,YAAY,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC;oBACpD,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;YAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;gBACrB,eAAM,CAAC,IAAI,CAAC,cAAc,YAAY,iBAAiB,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,gBAAwB,CAAC;QAClD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAClF,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAOhB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,oBAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEpE,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,OAAO;oBACL,UAAU,EAAE,CAAC;oBACb,SAAS,EAAE,CAAC;oBACZ,kBAAkB,EAAE,KAAK;oBACzB,UAAU,EAAE,IAAI;oBAChB,UAAU,EAAE,IAAI;iBACjB,CAAC;YACJ,CAAC;YAED,MAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACxC,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,IAAI,UAAU,GAAgB,IAAI,CAAC;YACnC,IAAI,UAAU,GAAgB,IAAI,CAAC;YAEnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC5C,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAGpC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;wBACxB,SAAS;oBACX,CAAC;oBAED,UAAU,EAAE,CAAC;oBACb,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC;oBAExB,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC;wBAC5C,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC3B,CAAC;oBAED,IAAI,CAAC,UAAU,IAAI,KAAK,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC;wBAC5C,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC3B,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;YAED,OAAO;gBACL,UAAU;gBACV,SAAS;gBACT,kBAAkB,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;gBAC/C,UAAU;gBACV,UAAU;aACX,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKO,WAAW,CAAC,KAAa;QAC/B,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAE9B,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAKD,eAAe;QACb,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QAGD,IAAI,CAAC,UAAU,GAAG,mBAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;YACtD,IAAI,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBAGlD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;gBAGpD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAG1D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBAEzD,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,gBAAgB,eAAe,oBAAoB,aAAa,iBAAiB,CAAC,CAAC;YAC5I,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,EAAE;YACD,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;IAC/D,CAAC;IAKD,cAAc;QACZ,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,gBAAgB;QAKpB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAE/C,MAAM,CAAC,SAAS,EAAE,aAAa,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAClE,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;aAC7B,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,6BAA6B,SAAS,gBAAgB,aAAa,oBAAoB,aAAa,iBAAiB,CAAC,CAAC;YAEnI,OAAO;gBACL,SAAS;gBACT,aAAa;gBACb,aAAa;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AArRD,gDAqRC;AAGY,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC"}