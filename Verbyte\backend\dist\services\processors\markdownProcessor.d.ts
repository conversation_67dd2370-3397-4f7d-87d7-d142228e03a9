import { DocumentType, TextExtractionResult, ProcessingOptions, IDocumentProcessor } from '@/types/document';
export declare class MarkdownProcessor implements IDocumentProcessor {
    supports(mimeType: string): boolean;
    getType(): DocumentType;
    process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    private extractFrontMatter;
    private markdownToText;
    private extractMetadata;
    private extractTitle;
    private createChunks;
    private countWords;
    private createBasicMetadata;
    private createErrorMetadata;
}
//# sourceMappingURL=markdownProcessor.d.ts.map