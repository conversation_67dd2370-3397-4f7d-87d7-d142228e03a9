import { DocumentType, TextExtractionResult, ProcessingOptions, IDocumentProcessor } from '@/types/document';
export declare class PowerPointProcessor implements IDocumentProcessor {
    supports(mimeType: string): boolean;
    getType(): DocumentType;
    process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    private cleanExtractedText;
    private extractMetadata;
    private estimateSlideCount;
    private detectImages;
    private detectNotes;
    private detectAnimations;
    private createChunks;
    private splitBySlides;
    private countWords;
    private createBasicMetadata;
    private createErrorMetadata;
}
//# sourceMappingURL=powerPointProcessor.d.ts.map