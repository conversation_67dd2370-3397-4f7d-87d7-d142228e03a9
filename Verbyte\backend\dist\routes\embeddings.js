"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.embeddingsRoutes = void 0;
const express_1 = require("express");
const router = (0, express_1.Router)();
exports.embeddingsRoutes = router;
router.post('/', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Embedding endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
//# sourceMappingURL=embeddings.js.map