export declare class FileCleanupService {
    private uploadService;
    private cleanupJob;
    constructor();
    cleanupTempFiles(olderThanHours?: number): Promise<number>;
    cleanupOrphanedFiles(): Promise<number>;
    cleanupFailedUploads(olderThanDays?: number): Promise<number>;
    getDiskUsage(): Promise<{
        totalFiles: number;
        totalSize: number;
        totalSizeFormatted: string;
        oldestFile: Date | null;
        newestFile: Date | null;
    }>;
    private formatBytes;
    startCleanupJob(): void;
    stopCleanupJob(): void;
    runManualCleanup(): Promise<{
        tempFiles: number;
        orphanedFiles: number;
        failedUploads: number;
    }>;
}
export declare const fileCleanupService: FileCleanupService;
//# sourceMappingURL=fileCleanup.d.ts.map