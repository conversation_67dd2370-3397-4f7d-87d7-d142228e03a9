"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CSVProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const papaparse_1 = __importDefault(require("papaparse"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class CSVProcessor {
    supports(mimeType) {
        return mimeType === 'text/csv' ||
            mimeType === 'application/csv' ||
            mimeType.includes('csv');
    }
    getType() {
        return document_1.DocumentType.CSV;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing CSV file: ${filePath}`);
            const csvContent = fs_1.default.readFileSync(filePath, 'utf-8');
            const parseOptions = {
                header: options?.csvHasHeaders ?? true,
                delimiter: options?.csvDelimiter || '',
                skipEmptyLines: true,
                transformHeader: (header) => header.trim(),
                transform: (value) => value.trim()
            };
            const parseResult = papaparse_1.default.parse(csvContent, parseOptions);
            if (parseResult.errors.length > 0) {
                logger_1.logger.warn(`CSV parsing warnings for ${filePath}:`, parseResult.errors);
            }
            const metadata = await this.extractMetadata(filePath, parseResult, csvContent);
            const text = this.convertToText(parseResult.data, parseOptions.header || false);
            const chunks = options?.chunkSize ?
                this.createChunks(text, parseResult.data, options.chunkSize, options.chunkOverlap || 0) :
                undefined;
            metadata.wordCount = this.countWords(text);
            metadata.characterCount = text.length;
            const result = {
                text,
                metadata,
                chunks,
                processingTime: Date.now() - startTime,
                success: true
            };
            logger_1.logger.debug(`CSV processing completed: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown CSV processing error';
            logger_1.logger.error(`CSV processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    async extractMetadata(filePath, parseResult, csvContent) {
        try {
            const stats = fs_1.default.statSync(filePath);
            const delimiter = this.detectDelimiter(csvContent);
            const hasHeaders = Array.isArray(parseResult.data[0]) ?
                false :
                Object.keys(parseResult.data[0] || {}).length > 0;
            const columns = hasHeaders && parseResult.data.length > 0 ?
                Object.keys(parseResult.data[0]) :
                [];
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: 'text/csv',
                size: stats.size,
                type: document_1.DocumentType.CSV,
                delimiter,
                hasHeaders,
                rowCount: parseResult.data.length,
                columnCount: hasHeaders ? columns.length : (Array.isArray(parseResult.data[0]) ? parseResult.data[0].length : 0),
                columns: hasHeaders ? columns : undefined,
                encoding: 'utf-8',
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime
            };
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract CSV metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    convertToText(data, hasHeaders) {
        if (!data || data.length === 0) {
            return '';
        }
        const textParts = [];
        if (hasHeaders && typeof data[0] === 'object') {
            const headers = Object.keys(data[0]);
            textParts.push(`CSV Data with columns: ${headers.join(', ')}\n`);
            data.forEach((row, index) => {
                const rowText = headers.map(header => `${header}: ${row[header] || ''}`).join(', ');
                textParts.push(`Row ${index + 1}: ${rowText}`);
            });
        }
        else {
            textParts.push('CSV Data:\n');
            data.forEach((row, index) => {
                if (Array.isArray(row)) {
                    textParts.push(`Row ${index + 1}: ${row.join(', ')}`);
                }
                else {
                    textParts.push(`Row ${index + 1}: ${String(row)}`);
                }
            });
        }
        return textParts.join('\n');
    }
    createChunks(text, data, chunkSize, overlap) {
        const chunks = [];
        const lines = text.split('\n');
        let currentPos = 0;
        const rowsPerChunk = Math.max(1, Math.floor(chunkSize / 100));
        for (let i = 0; i < lines.length; i += rowsPerChunk) {
            const endIndex = Math.min(i + rowsPerChunk, lines.length);
            const chunkLines = lines.slice(i, endIndex);
            const chunkText = chunkLines.join('\n');
            chunks.push({
                id: `csv_chunk_${chunks.length}`,
                text: chunkText,
                startIndex: currentPos,
                endIndex: currentPos + chunkText.length,
                metadata: {
                    source: 'csv',
                    chunkIndex: chunks.length,
                    startRow: i,
                    endRow: endIndex - 1,
                    rowCount: chunkLines.length
                }
            });
            currentPos += chunkText.length + 1;
        }
        return chunks;
    }
    detectDelimiter(csvContent) {
        const delimiters = [',', ';', '\t', '|'];
        const sample = csvContent.split('\n').slice(0, 5).join('\n');
        let bestDelimiter = ',';
        let maxCount = 0;
        for (const delimiter of delimiters) {
            const count = (sample.match(new RegExp(`\\${delimiter}`, 'g')) || []).length;
            if (count > maxCount) {
                maxCount = count;
                bestDelimiter = delimiter;
            }
        }
        return bestDelimiter;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'text/csv',
            size: stats.size,
            type: document_1.DocumentType.CSV,
            delimiter: ',',
            hasHeaders: true,
            rowCount: 0,
            columnCount: 0,
            encoding: 'utf-8',
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.CSVProcessor = CSVProcessor;
//# sourceMappingURL=csvProcessor.js.map