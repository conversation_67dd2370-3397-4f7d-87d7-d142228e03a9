export declare enum DocumentType {
    PDF = "pdf",
    DOCX = "docx",
    CSV = "csv",
    MARKDOWN = "markdown",
    JSON = "json",
    IMAGE = "image",
    POWERPOINT = "powerpoint",
    TEXT = "text"
}
export declare enum ProcessingStage {
    PENDING = "pending",
    EXTRACTING_TEXT = "extracting_text",
    PROCESSING_METADATA = "processing_metadata",
    COMPLETED = "completed",
    FAILED = "failed"
}
export interface DocumentMetadata {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    type: DocumentType;
    pages?: number;
    wordCount?: number;
    characterCount?: number;
    language?: string;
    encoding?: string;
    createdAt?: Date;
    modifiedAt?: Date;
    author?: string;
    title?: string;
    subject?: string;
    keywords?: string[];
    [key: string]: any;
}
export interface TextExtractionResult {
    text: string;
    metadata: DocumentMetadata;
    chunks?: TextChunk[];
    processingTime: number;
    success: boolean;
    error?: string;
}
export interface TextChunk {
    id: string;
    text: string;
    startIndex: number;
    endIndex: number;
    page?: number;
    section?: string;
    metadata?: Record<string, any>;
}
export interface PDFMetadata extends DocumentMetadata {
    pdfVersion?: string;
    isEncrypted?: boolean;
    hasImages?: boolean;
    hasAnnotations?: boolean;
    producer?: string;
    creator?: string;
}
export interface DOCXMetadata extends DocumentMetadata {
    hasImages?: boolean;
    hasHeaders?: boolean;
    hasFooters?: boolean;
    hasTables?: boolean;
    styleCount?: number;
}
export interface CSVMetadata extends DocumentMetadata {
    delimiter?: string;
    hasHeaders?: boolean;
    rowCount?: number;
    columnCount?: number;
    columns?: string[];
    encoding?: string;
}
export interface ImageMetadata extends DocumentMetadata {
    width?: number;
    height?: number;
    format?: string;
    colorSpace?: string;
    hasText?: boolean;
    ocrLanguage?: string;
    confidence?: number;
}
export interface PowerPointMetadata extends DocumentMetadata {
    slideCount?: number;
    hasImages?: boolean;
    hasNotes?: boolean;
    hasAnimations?: boolean;
    template?: string;
}
export interface JSONMetadata extends DocumentMetadata {
    structure?: 'object' | 'array' | 'mixed';
    depth?: number;
    keyCount?: number;
    hasNestedObjects?: boolean;
    hasArrays?: boolean;
}
export interface MarkdownMetadata extends DocumentMetadata {
    hasHeaders?: boolean;
    hasLinks?: boolean;
    hasImages?: boolean;
    hasTables?: boolean;
    hasCodeBlocks?: boolean;
    frontMatter?: Record<string, any>;
}
export interface ProcessingOptions {
    extractImages?: boolean;
    preserveFormatting?: boolean;
    includeMetadata?: boolean;
    chunkSize?: number;
    chunkOverlap?: number;
    ocrLanguage?: string;
    csvDelimiter?: string;
    csvHasHeaders?: boolean;
    maxFileSize?: number;
    timeout?: number;
}
export interface DocumentProcessorConfig {
    maxFileSize: number;
    supportedTypes: DocumentType[];
    defaultOptions: ProcessingOptions;
    ocrEnabled: boolean;
    tempDirectory: string;
    cleanupAfterProcessing: boolean;
}
export declare enum DocumentProcessingError {
    UNSUPPORTED_FORMAT = "UNSUPPORTED_FORMAT",
    FILE_TOO_LARGE = "FILE_TOO_LARGE",
    CORRUPTED_FILE = "CORRUPTED_FILE",
    EXTRACTION_FAILED = "EXTRACTION_FAILED",
    OCR_FAILED = "OCR_FAILED",
    TIMEOUT = "TIMEOUT",
    INSUFFICIENT_MEMORY = "INSUFFICIENT_MEMORY",
    PERMISSION_DENIED = "PERMISSION_DENIED",
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
}
export interface ProcessingError {
    code: DocumentProcessingError;
    message: string;
    details?: string;
    stage?: ProcessingStage;
    timestamp: Date;
}
export interface IDocumentProcessor {
    process(filePath: string, options?: ProcessingOptions): Promise<TextExtractionResult>;
    supports(mimeType: string): boolean;
    getType(): DocumentType;
}
export interface BatchProcessingResult {
    totalFiles: number;
    successfulFiles: number;
    failedFiles: number;
    results: TextExtractionResult[];
    errors: ProcessingError[];
    processingTime: number;
}
//# sourceMappingURL=document.d.ts.map