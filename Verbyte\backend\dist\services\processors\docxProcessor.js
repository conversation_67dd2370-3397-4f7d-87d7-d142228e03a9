"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DOCXProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const mammoth_1 = __importDefault(require("mammoth"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class DOCXProcessor {
    supports(mimeType) {
        return mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            mimeType === 'application/msword' ||
            mimeType.includes('word') ||
            mimeType.includes('docx');
    }
    getType() {
        return document_1.DocumentType.DOCX;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing DOCX file: ${filePath}`);
            const buffer = fs_1.default.readFileSync(filePath);
            const mammothOptions = {
                convertImage: mammoth_1.default.images.imgElement((image) => {
                    return image.read("base64").then((imageBuffer) => {
                        return {
                            src: `data:${image.contentType};base64,${imageBuffer}`
                        };
                    });
                }),
                includeDefaultStyleMap: true,
                includeEmbeddedStyleMap: true
            };
            const [textResult, htmlResult] = await Promise.all([
                mammoth_1.default.extractRawText({ buffer }),
                options?.preserveFormatting ? mammoth_1.default.convertToHtml({ buffer }, mammothOptions) : Promise.resolve(null)
            ]);
            const metadata = await this.extractMetadata(filePath, textResult.value, htmlResult?.value);
            const chunks = options?.chunkSize ?
                this.createChunks(textResult.value, options.chunkSize, options.chunkOverlap || 0) :
                undefined;
            metadata.wordCount = this.countWords(textResult.value);
            metadata.characterCount = textResult.value.length;
            const result = {
                text: textResult.value,
                metadata,
                chunks,
                processingTime: Date.now() - startTime,
                success: true
            };
            if (textResult.messages.length > 0) {
                logger_1.logger.warn(`DOCX processing warnings for ${filePath}:`, textResult.messages);
            }
            logger_1.logger.debug(`DOCX processing completed: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown DOCX processing error';
            logger_1.logger.error(`DOCX processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    async extractMetadata(filePath, text, html) {
        try {
            const stats = fs_1.default.statSync(filePath);
            const hasImages = html ? html.includes('<img') : false;
            const hasHeaders = this.hasHeaders(text);
            const hasFooters = this.hasFooters(text);
            const hasTables = html ? html.includes('<table') : this.detectTables(text);
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                size: stats.size,
                type: document_1.DocumentType.DOCX,
                hasImages,
                hasHeaders,
                hasFooters,
                hasTables,
                styleCount: html ? this.countStyles(html) : 0,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime
            };
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract DOCX metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    createChunks(text, chunkSize, overlap) {
        const chunks = [];
        let currentPos = 0;
        while (currentPos < text.length) {
            const endPos = Math.min(currentPos + chunkSize, text.length);
            const chunkText = text.substring(currentPos, endPos);
            chunks.push({
                id: `docx_chunk_${chunks.length}`,
                text: chunkText,
                startIndex: currentPos,
                endIndex: endPos,
                metadata: {
                    source: 'docx',
                    chunkIndex: chunks.length
                }
            });
            currentPos = endPos - overlap;
            if (currentPos >= text.length)
                break;
        }
        return chunks;
    }
    hasHeaders(text) {
        const headerPatterns = [
            /^[A-Z][A-Z\s]{2,}$/m,
            /^\d+\.\s+[A-Z]/m,
            /^Chapter\s+\d+/mi,
            /^Section\s+\d+/mi,
            /^[A-Z][a-z]+\s+[A-Z][a-z]+$/m
        ];
        return headerPatterns.some(pattern => pattern.test(text));
    }
    hasFooters(text) {
        const footerPatterns = [
            /Page\s+\d+/i,
            /\d+\s*$/m,
            /©\s*\d{4}/,
            /Copyright/i
        ];
        return footerPatterns.some(pattern => pattern.test(text));
    }
    detectTables(text) {
        const tablePatterns = [
            /\t.*\t.*\t/,
            /\|.*\|.*\|/,
            /^\s*\w+\s+\w+\s+\w+/m
        ];
        return tablePatterns.some(pattern => pattern.test(text));
    }
    countStyles(html) {
        const styleMatches = html.match(/style="[^"]*"/g);
        return styleMatches ? styleMatches.length : 0;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            size: stats.size,
            type: document_1.DocumentType.DOCX,
            hasImages: false,
            hasHeaders: false,
            hasFooters: false,
            hasTables: false,
            styleCount: 0,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.DOCXProcessor = DOCXProcessor;
//# sourceMappingURL=docxProcessor.js.map