"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = void 0;
const express_1 = require("express");
const router = (0, express_1.Router)();
exports.authRoutes = router;
router.post('/register', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Authentication endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
router.post('/login', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Authentication endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
router.post('/refresh', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Authentication endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
router.post('/logout', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Authentication endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
//# sourceMappingURL=auth.js.map