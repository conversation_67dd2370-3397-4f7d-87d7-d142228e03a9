"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarkdownProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class MarkdownProcessor {
    supports(mimeType) {
        return mimeType === 'text/markdown' ||
            mimeType === 'text/x-markdown' ||
            mimeType.includes('markdown');
    }
    getType() {
        return document_1.DocumentType.MARKDOWN;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing Markdown file: ${filePath}`);
            const markdownContent = fs_1.default.readFileSync(filePath, 'utf-8');
            const { content, frontMatter } = this.extractFrontMatter(markdownContent);
            const metadata = await this.extractMetadata(filePath, content, frontMatter);
            const text = options?.preserveFormatting ? content : this.markdownToText(content);
            const chunks = options?.chunkSize ?
                this.createChunks(text, options.chunkSize, options.chunkOverlap || 0) :
                undefined;
            metadata.wordCount = this.countWords(text);
            metadata.characterCount = text.length;
            const result = {
                text,
                metadata,
                chunks,
                processingTime: Date.now() - startTime,
                success: true
            };
            logger_1.logger.debug(`Markdown processing completed: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown Markdown processing error';
            logger_1.logger.error(`Markdown processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    extractFrontMatter(content) {
        const frontMatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n/;
        const match = content.match(frontMatterRegex);
        if (!match) {
            return { content };
        }
        try {
            const frontMatterText = match[1];
            const frontMatter = {};
            frontMatterText.split('\n').forEach(line => {
                const colonIndex = line.indexOf(':');
                if (colonIndex > 0) {
                    const key = line.substring(0, colonIndex).trim();
                    const value = line.substring(colonIndex + 1).trim();
                    frontMatter[key] = value.replace(/^["']|["']$/g, '');
                }
            });
            return {
                content: content.replace(frontMatterRegex, ''),
                frontMatter
            };
        }
        catch (error) {
            logger_1.logger.warn('Failed to parse front matter, treating as regular content');
            return { content };
        }
    }
    markdownToText(markdown) {
        return markdown
            .replace(/```[\s\S]*?```/g, '')
            .replace(/`[^`]*`/g, '')
            .replace(/^#{1,6}\s+/gm, '')
            .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
            .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1')
            .replace(/\*\*([^*]+)\*\*/g, '$1')
            .replace(/\*([^*]+)\*/g, '$1')
            .replace(/__([^_]+)__/g, '$1')
            .replace(/_([^_]+)_/g, '$1')
            .replace(/~~([^~]+)~~/g, '$1')
            .replace(/^---+$/gm, '')
            .replace(/^\s*[-*+]\s+/gm, '')
            .replace(/^\s*\d+\.\s+/gm, '')
            .replace(/^\s*>\s+/gm, '')
            .replace(/\n\s*\n/g, '\n\n')
            .trim();
    }
    async extractMetadata(filePath, content, frontMatter) {
        try {
            const stats = fs_1.default.statSync(filePath);
            const hasHeaders = /^#{1,6}\s+/m.test(content);
            const hasLinks = /\[([^\]]+)\]\([^)]+\)/.test(content);
            const hasImages = /!\[([^\]]*)\]\([^)]+\)/.test(content);
            const hasTables = /\|.*\|/.test(content);
            const hasCodeBlocks = /```/.test(content);
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: 'text/markdown',
                size: stats.size,
                type: document_1.DocumentType.MARKDOWN,
                hasHeaders,
                hasLinks,
                hasImages,
                hasTables,
                hasCodeBlocks,
                frontMatter,
                title: frontMatter?.title || this.extractTitle(content),
                author: frontMatter?.author,
                createdAt: frontMatter?.date ? new Date(frontMatter.date) : stats.birthtime,
                modifiedAt: stats.mtime
            };
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract Markdown metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    extractTitle(content) {
        const titleMatch = content.match(/^#\s+(.+)$/m);
        return titleMatch ? titleMatch[1].trim() : undefined;
    }
    createChunks(text, chunkSize, overlap) {
        const chunks = [];
        let currentPos = 0;
        while (currentPos < text.length) {
            const endPos = Math.min(currentPos + chunkSize, text.length);
            const chunkText = text.substring(currentPos, endPos);
            chunks.push({
                id: `markdown_chunk_${chunks.length}`,
                text: chunkText,
                startIndex: currentPos,
                endIndex: endPos,
                metadata: {
                    source: 'markdown',
                    chunkIndex: chunks.length
                }
            });
            currentPos = endPos - overlap;
            if (currentPos >= text.length)
                break;
        }
        return chunks;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'text/markdown',
            size: stats.size,
            type: document_1.DocumentType.MARKDOWN,
            hasHeaders: false,
            hasLinks: false,
            hasImages: false,
            hasTables: false,
            hasCodeBlocks: false,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.MarkdownProcessor = MarkdownProcessor;
//# sourceMappingURL=markdownProcessor.js.map