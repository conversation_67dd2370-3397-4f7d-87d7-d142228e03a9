"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadService = void 0;
const fs_1 = __importDefault(require("fs"));
const Upload_1 = require("@/models/Upload");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
class UploadService {
    async createUpload(data) {
        try {
            const upload = new Upload_1.Upload({
                ...data,
                status: Upload_1.UploadStatus.UPLOADED,
                processingStatus: Upload_1.ProcessingStatus.PENDING,
                uploadProgress: 100,
                processingProgress: 0,
                startedAt: new Date(),
                metadata: {}
            });
            await upload.save();
            logger_1.logger.info(`Created upload record: ${upload._id} for file: ${data.originalName}`);
            return upload;
        }
        catch (error) {
            logger_1.logger.error('Failed to create upload record:', error);
            throw new errorHandler_1.AppError('Failed to create upload record', 500);
        }
    }
    async getUploadById(uploadId) {
        try {
            const upload = await Upload_1.Upload.findById(uploadId);
            return upload;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get upload ${uploadId}:`, error);
            throw new errorHandler_1.AppError('Failed to retrieve upload', 500);
        }
    }
    async getUploadsByUserId(userId, limit = 50, offset = 0) {
        try {
            const uploads = await Upload_1.Upload.find({ userId })
                .sort({ createdAt: -1 })
                .limit(limit)
                .skip(offset);
            return uploads;
        }
        catch (error) {
            logger_1.logger.error(`Failed to get uploads for user ${userId}:`, error);
            throw new errorHandler_1.AppError('Failed to retrieve uploads', 500);
        }
    }
    async getAllUploads(limit = 50, offset = 0) {
        try {
            const uploads = await Upload_1.Upload.find()
                .sort({ createdAt: -1 })
                .limit(limit)
                .skip(offset);
            return uploads;
        }
        catch (error) {
            logger_1.logger.error('Failed to get all uploads:', error);
            throw new errorHandler_1.AppError('Failed to retrieve uploads', 500);
        }
    }
    async updateProgress(uploadId, uploadProgress, processingProgress) {
        try {
            const upload = await Upload_1.Upload.findById(uploadId);
            if (!upload) {
                throw new errorHandler_1.ValidationError('Upload not found');
            }
            await upload.updateProgress(uploadProgress, processingProgress);
            logger_1.logger.debug(`Updated progress for upload ${uploadId}: upload=${uploadProgress}%, processing=${processingProgress}%`);
            return upload;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update progress for upload ${uploadId}:`, error);
            throw error instanceof errorHandler_1.AppError ? error : new errorHandler_1.AppError('Failed to update upload progress', 500);
        }
    }
    async updateStatus(uploadId, status, processingStatus) {
        try {
            const upload = await Upload_1.Upload.findById(uploadId);
            if (!upload) {
                throw new errorHandler_1.ValidationError('Upload not found');
            }
            await upload.setStatus(status, processingStatus);
            logger_1.logger.info(`Updated status for upload ${uploadId}: ${status}${processingStatus ? ` / ${processingStatus}` : ''}`);
            return upload;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update status for upload ${uploadId}:`, error);
            throw error instanceof errorHandler_1.AppError ? error : new errorHandler_1.AppError('Failed to update upload status', 500);
        }
    }
    async setError(uploadId, error, code) {
        try {
            const upload = await Upload_1.Upload.findById(uploadId);
            if (!upload) {
                throw new errorHandler_1.ValidationError('Upload not found');
            }
            await upload.setError(error, code);
            logger_1.logger.error(`Set error for upload ${uploadId}: ${error instanceof Error ? error.message : error}`);
            return upload;
        }
        catch (err) {
            logger_1.logger.error(`Failed to set error for upload ${uploadId}:`, err);
            throw err instanceof errorHandler_1.AppError ? err : new errorHandler_1.AppError('Failed to set upload error', 500);
        }
    }
    async deleteUpload(uploadId) {
        try {
            const upload = await Upload_1.Upload.findById(uploadId);
            if (!upload) {
                throw new errorHandler_1.ValidationError('Upload not found');
            }
            if (upload.path && fs_1.default.existsSync(upload.path)) {
                fs_1.default.unlinkSync(upload.path);
                logger_1.logger.debug(`Deleted file: ${upload.path}`);
            }
            await Upload_1.Upload.findByIdAndDelete(uploadId);
            logger_1.logger.info(`Deleted upload ${uploadId} and associated file`);
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete upload ${uploadId}:`, error);
            throw error instanceof errorHandler_1.AppError ? error : new errorHandler_1.AppError('Failed to delete upload', 500);
        }
    }
    async getProgress(uploadId) {
        try {
            const upload = await Upload_1.Upload.findById(uploadId);
            if (!upload) {
                throw new errorHandler_1.ValidationError('Upload not found');
            }
            return {
                uploadId: upload._id.toString(),
                uploadProgress: upload.uploadProgress,
                processingProgress: upload.processingProgress,
                status: upload.status,
                processingStatus: upload.processingStatus,
                error: upload.error?.message
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get progress for upload ${uploadId}:`, error);
            throw error instanceof errorHandler_1.AppError ? error : new errorHandler_1.AppError('Failed to get upload progress', 500);
        }
    }
    async getPendingUploads() {
        try {
            const uploads = await Upload_1.Upload.findPendingProcessing();
            return uploads;
        }
        catch (error) {
            logger_1.logger.error('Failed to get pending uploads:', error);
            throw new errorHandler_1.AppError('Failed to retrieve pending uploads', 500);
        }
    }
    async cleanupFailedUploads(olderThanDays = 7) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
            const failedUploads = await Upload_1.Upload.find({
                status: Upload_1.UploadStatus.FAILED,
                createdAt: { $lt: cutoffDate }
            });
            let cleanedCount = 0;
            for (const upload of failedUploads) {
                try {
                    if (upload.path && fs_1.default.existsSync(upload.path)) {
                        fs_1.default.unlinkSync(upload.path);
                    }
                    await Upload_1.Upload.findByIdAndDelete(upload._id);
                    cleanedCount++;
                }
                catch (error) {
                    logger_1.logger.error(`Failed to cleanup upload ${upload._id}:`, error);
                }
            }
            logger_1.logger.info(`Cleaned up ${cleanedCount} failed uploads older than ${olderThanDays} days`);
            return cleanedCount;
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup failed uploads:', error);
            throw new errorHandler_1.AppError('Failed to cleanup failed uploads', 500);
        }
    }
    async getUploadStats() {
        try {
            const [total, statusStats, processingStats, sizeStats] = await Promise.all([
                Upload_1.Upload.countDocuments(),
                Upload_1.Upload.aggregate([
                    { $group: { _id: '$status', count: { $sum: 1 } } }
                ]),
                Upload_1.Upload.aggregate([
                    { $group: { _id: '$processingStatus', count: { $sum: 1 } } }
                ]),
                Upload_1.Upload.aggregate([
                    { $group: { _id: null, totalSize: { $sum: '$size' } } }
                ])
            ]);
            const byStatus = Object.values(Upload_1.UploadStatus).reduce((acc, status) => {
                acc[status] = 0;
                return acc;
            }, {});
            const byProcessingStatus = Object.values(Upload_1.ProcessingStatus).reduce((acc, status) => {
                acc[status] = 0;
                return acc;
            }, {});
            statusStats.forEach((stat) => {
                byStatus[stat._id] = stat.count;
            });
            processingStats.forEach((stat) => {
                byProcessingStatus[stat._id] = stat.count;
            });
            const totalSize = sizeStats[0]?.totalSize || 0;
            return {
                total,
                byStatus,
                byProcessingStatus,
                totalSize
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get upload statistics:', error);
            throw new errorHandler_1.AppError('Failed to get upload statistics', 500);
        }
    }
}
exports.UploadService = UploadService;
//# sourceMappingURL=uploadService.js.map