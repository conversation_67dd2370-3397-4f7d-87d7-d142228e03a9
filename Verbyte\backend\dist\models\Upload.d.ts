import mongoose, { Document } from 'mongoose';
export declare enum UploadStatus {
    PENDING = "pending",
    UPLOADING = "uploading",
    UPLOADED = "uploaded",
    PROCESSING = "processing",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled"
}
export declare enum ProcessingStatus {
    PENDING = "pending",
    EXTRACTING_TEXT = "extracting_text",
    CHUNKING = "chunking",
    GENERATING_EMBEDDINGS = "generating_embeddings",
    STORING_VECTORS = "storing_vectors",
    COMPLETED = "completed",
    FAILED = "failed"
}
export interface IUpload extends Document {
    _id: mongoose.Types.ObjectId;
    userId?: mongoose.Types.ObjectId;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    path: string;
    status: UploadStatus;
    processingStatus: ProcessingStatus;
    uploadProgress: number;
    processingProgress: number;
    metadata: {
        chunks?: number;
        pages?: number;
        extractedText?: boolean;
        embeddingsGenerated?: boolean;
        vectorsStored?: boolean;
        qdrantCollectionId?: string;
        documentIds?: string[];
        [key: string]: any;
    };
    error?: {
        message: string;
        code?: string;
        stack?: string;
        timestamp: Date;
    };
    startedAt: Date;
    completedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export declare const Upload: mongoose.Model<IUpload, {}, {}, {}, mongoose.Document<unknown, {}, IUpload, {}, {}> & IUpload & Required<{
    _id: mongoose.Types.ObjectId;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Upload.d.ts.map