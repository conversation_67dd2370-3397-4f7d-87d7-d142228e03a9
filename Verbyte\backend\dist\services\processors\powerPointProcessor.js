"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerPointProcessor = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const office_text_extractor_1 = require("office-text-extractor");
const logger_1 = require("@/utils/logger");
const document_1 = require("@/types/document");
class PowerPointProcessor {
    supports(mimeType) {
        return mimeType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
            mimeType === 'application/vnd.ms-powerpoint' ||
            mimeType.includes('powerpoint') ||
            mimeType.includes('presentation');
    }
    getType() {
        return document_1.DocumentType.POWERPOINT;
    }
    async process(filePath, options) {
        const startTime = Date.now();
        try {
            logger_1.logger.debug(`Processing PowerPoint file: ${filePath}`);
            const buffer = fs_1.default.readFileSync(filePath);
            const extractor = (0, office_text_extractor_1.getTextExtractor)();
            const extractedText = await extractor.extractText({ input: buffer, type: 'buffer' });
            const text = this.cleanExtractedText(extractedText);
            const metadata = await this.extractMetadata(filePath, text);
            const chunks = options?.chunkSize ?
                this.createChunks(text, options.chunkSize, options.chunkOverlap || 0) :
                undefined;
            metadata.wordCount = this.countWords(text);
            metadata.characterCount = text.length;
            const result = {
                text,
                metadata,
                chunks,
                processingTime: Date.now() - startTime,
                success: true
            };
            logger_1.logger.debug(`PowerPoint processing completed: ${filePath} (${result.processingTime}ms)`);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown PowerPoint processing error';
            logger_1.logger.error(`PowerPoint processing failed: ${filePath}`, error);
            return {
                text: '',
                metadata: this.createErrorMetadata(filePath, errorMessage),
                processingTime: Date.now() - startTime,
                success: false,
                error: errorMessage
            };
        }
    }
    cleanExtractedText(text) {
        return text
            .replace(/\s+/g, ' ')
            .replace(/---+/g, '\n\n')
            .replace(/\n\s*\n/g, '\n\n')
            .trim();
    }
    async extractMetadata(filePath, text) {
        try {
            const stats = fs_1.default.statSync(filePath);
            const slideCount = this.estimateSlideCount(text);
            const hasImages = this.detectImages(text);
            const hasNotes = this.detectNotes(text);
            const hasAnimations = this.detectAnimations(text);
            const metadata = {
                filename: path_1.default.basename(filePath),
                originalName: path_1.default.basename(filePath),
                mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                size: stats.size,
                type: document_1.DocumentType.POWERPOINT,
                slideCount,
                hasImages,
                hasNotes,
                hasAnimations,
                createdAt: stats.birthtime,
                modifiedAt: stats.mtime
            };
            return metadata;
        }
        catch (error) {
            logger_1.logger.warn(`Failed to extract PowerPoint metadata: ${filePath}`, error);
            return this.createBasicMetadata(filePath);
        }
    }
    estimateSlideCount(text) {
        const slideIndicators = [
            /Slide\s+\d+/gi,
            /Page\s+\d+/gi,
            /^\d+\.\s+/gm,
        ];
        let maxCount = 1;
        slideIndicators.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                maxCount = Math.max(maxCount, matches.length);
            }
        });
        const estimatedByLength = Math.max(1, Math.floor(text.length / 500));
        return Math.min(maxCount, estimatedByLength);
    }
    detectImages(text) {
        const imageIndicators = [
            /\[image\]/gi,
            /\[picture\]/gi,
            /\[graphic\]/gi,
            /image\s*\d+/gi,
            /figure\s*\d+/gi
        ];
        return imageIndicators.some(pattern => pattern.test(text));
    }
    detectNotes(text) {
        const noteIndicators = [
            /notes?:/gi,
            /speaker\s+notes?/gi,
            /\[note\]/gi
        ];
        return noteIndicators.some(pattern => pattern.test(text));
    }
    detectAnimations(text) {
        const animationIndicators = [
            /animation/gi,
            /transition/gi,
            /effect/gi,
            /\[animated\]/gi
        ];
        return animationIndicators.some(pattern => pattern.test(text));
    }
    createChunks(text, chunkSize, overlap) {
        const chunks = [];
        const slideTexts = this.splitBySlides(text);
        if (slideTexts.length > 1) {
            slideTexts.forEach((slideText, index) => {
                if (slideText.trim()) {
                    chunks.push({
                        id: `ppt_slide_${index + 1}`,
                        text: slideText.trim(),
                        startIndex: 0,
                        endIndex: slideText.length,
                        metadata: {
                            source: 'powerpoint',
                            slideNumber: index + 1,
                            chunkIndex: chunks.length
                        }
                    });
                }
            });
        }
        else {
            let currentPos = 0;
            while (currentPos < text.length) {
                const endPos = Math.min(currentPos + chunkSize, text.length);
                const chunkText = text.substring(currentPos, endPos);
                chunks.push({
                    id: `ppt_chunk_${chunks.length}`,
                    text: chunkText,
                    startIndex: currentPos,
                    endIndex: endPos,
                    metadata: {
                        source: 'powerpoint',
                        chunkIndex: chunks.length
                    }
                });
                currentPos = endPos - overlap;
                if (currentPos >= text.length)
                    break;
            }
        }
        return chunks;
    }
    splitBySlides(text) {
        const separators = [
            /\n\s*Slide\s+\d+/gi,
            /\n\s*Page\s+\d+/gi,
            /\n\s*\d+\.\s+/g,
            /\n\s*---+\s*\n/g
        ];
        for (const separator of separators) {
            const parts = text.split(separator);
            if (parts.length > 1) {
                return parts.filter(part => part.trim().length > 0);
            }
        }
        return [text];
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    createBasicMetadata(filePath) {
        const stats = fs_1.default.statSync(filePath);
        return {
            filename: path_1.default.basename(filePath),
            originalName: path_1.default.basename(filePath),
            mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            size: stats.size,
            type: document_1.DocumentType.POWERPOINT,
            slideCount: 1,
            hasImages: false,
            hasNotes: false,
            hasAnimations: false,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime
        };
    }
    createErrorMetadata(filePath, error) {
        const basicMetadata = this.createBasicMetadata(filePath);
        return {
            ...basicMetadata,
            error
        };
    }
}
exports.PowerPointProcessor = PowerPointProcessor;
//# sourceMappingURL=powerPointProcessor.js.map