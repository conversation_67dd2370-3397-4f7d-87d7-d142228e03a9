import { Router, Request, Response } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '@/middleware/errorHandler';
import {
  uploadSingleFile,
  uploadMultipleFiles,
  validateUploadedFile
} from '@/middleware/upload';
import { UploadService } from '@/services/uploadService';
import { IntegratedDocumentProcessingService } from '@/services/documentProcessingService';
import { fileCleanupService } from '@/utils/fileCleanup';
import { logger } from '@/utils/logger';
import { ProcessingOptions } from '@/types/document';

const router = Router();
const uploadService = new UploadService();
const documentProcessingService = new IntegratedDocumentProcessingService();

/**
 * Upload single file
 * POST /v1/ingest/file
 */
router.post('/file',
  uploadSingleFile,
  validateUploadedFile,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.file) {
        res.status(400).json({
          status: 'error',
          message: 'No file uploaded',
          code: 'NO_FILE'
        });
        return;
      }

      // Create upload record
      const upload = await uploadService.createUpload({
        filename: req.file.filename,
        originalName: req.file.originalname,
        mimeType: req.file.mimetype,
        size: req.file.size,
        path: req.file.path,
        userId: req.user?.id // Will be available when auth is implemented
      });

      logger.info(`File uploaded successfully: ${req.file.originalname} (${upload._id})`);

      res.status(201).json({
        status: 'success',
        message: 'File uploaded successfully',
        data: {
          uploadId: upload._id,
          filename: upload.originalName,
          size: upload.size,
          mimeType: upload.mimeType,
          status: upload.status,
          processingStatus: upload.processingStatus,
          uploadProgress: upload.uploadProgress,
          processingProgress: upload.processingProgress,
          createdAt: upload.createdAt
        }
      });
    } catch (error) {
      logger.error('File upload error:', error);
      throw error;
    }
  })
);

/**
 * Upload multiple files
 * POST /v1/ingest/files
 */
router.post('/files',
  uploadMultipleFiles,
  validateUploadedFile,
  asyncHandler(async (req: Request, res: Response): Promise<void> => {
    try {
      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        res.status(400).json({
          status: 'error',
          message: 'No files uploaded',
          code: 'NO_FILES'
        });
        return;
      }

      // Create upload records for all files
      const uploads = await Promise.all(
        req.files.map(file =>
          uploadService.createUpload({
            filename: file.filename,
            originalName: file.originalname,
            mimeType: file.mimetype,
            size: file.size,
            path: file.path,
            userId: req.user?.id
          })
        )
      );

      logger.info(`${uploads.length} files uploaded successfully`);

      res.status(201).json({
        status: 'success',
        message: `${uploads.length} files uploaded successfully`,
        data: uploads.map(upload => ({
          uploadId: upload._id,
          filename: upload.originalName,
          size: upload.size,
          mimeType: upload.mimeType,
          status: upload.status,
          processingStatus: upload.processingStatus,
          uploadProgress: upload.uploadProgress,
          processingProgress: upload.processingProgress,
          createdAt: upload.createdAt
        }))
      });
    } catch (error) {
      logger.error('Multiple files upload error:', error);
      throw error;
    }
  })
);

/**
 * Process uploaded document
 * POST /v1/ingest/process/:uploadId
 */
router.post('/process/:uploadId', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { uploadId } = req.params;
    const options: ProcessingOptions = {
      extractImages: req.body.extractImages || false,
      preserveFormatting: req.body.preserveFormatting || true,
      includeMetadata: req.body.includeMetadata || true,
      chunkSize: req.body.chunkSize || 1000,
      chunkOverlap: req.body.chunkOverlap || 200,
      ocrLanguage: req.body.ocrLanguage || 'eng',
      csvHasHeaders: req.body.csvHasHeaders !== false,
      timeout: req.body.timeout || 300000
    };

    const result = await documentProcessingService.processUploadedDocument(uploadId, options);

    res.json({
      status: 'success',
      message: 'Document processed successfully',
      data: {
        uploadId,
        success: result.success,
        text: result.text,
        metadata: result.metadata,
        chunks: result.chunks,
        processingTime: result.processingTime,
        error: result.error
      }
    });
  } catch (error) {
    logger.error('Document processing error:', error);
    throw error;
  }
}));

/**
 * Process multiple documents in batch
 * POST /v1/ingest/process-batch
 */
router.post('/process-batch', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { uploadIds } = req.body;

    if (!Array.isArray(uploadIds) || uploadIds.length === 0) {
      res.status(400).json({
        status: 'error',
        message: 'uploadIds array is required',
        code: 'INVALID_INPUT'
      });
      return;
    }

    const options: ProcessingOptions = {
      extractImages: req.body.extractImages || false,
      preserveFormatting: req.body.preserveFormatting || true,
      includeMetadata: req.body.includeMetadata || true,
      chunkSize: req.body.chunkSize || 1000,
      chunkOverlap: req.body.chunkOverlap || 200,
      ocrLanguage: req.body.ocrLanguage || 'eng',
      csvHasHeaders: req.body.csvHasHeaders !== false,
      timeout: req.body.timeout || 300000
    };

    const results = await documentProcessingService.processBatchUploads(uploadIds, options);

    res.json({
      status: 'success',
      message: `Processed ${results.length} documents`,
      data: {
        results: results.map(r => ({
          uploadId: r.uploadId,
          success: r.result.success,
          text: r.result.text,
          metadata: r.result.metadata,
          chunks: r.result.chunks,
          processingTime: r.result.processingTime,
          error: r.result.error
        })),
        summary: {
          total: results.length,
          successful: results.filter(r => r.result.success).length,
          failed: results.filter(r => !r.result.success).length
        }
      }
    });
  } catch (error) {
    logger.error('Batch processing error:', error);
    throw error;
  }
}));

/**
 * Process pending uploads
 * POST /v1/ingest/process-pending
 */
router.post('/process-pending', asyncHandler(async (req: Request, res: Response) => {
  try {
    const options: ProcessingOptions = {
      extractImages: req.body.extractImages || false,
      preserveFormatting: req.body.preserveFormatting || true,
      includeMetadata: req.body.includeMetadata || true,
      chunkSize: req.body.chunkSize || 1000,
      chunkOverlap: req.body.chunkOverlap || 200,
      ocrLanguage: req.body.ocrLanguage || 'eng',
      csvHasHeaders: req.body.csvHasHeaders !== false,
      timeout: req.body.timeout || 300000
    };

    const processedCount = await documentProcessingService.processPendingUploads(options);

    res.json({
      status: 'success',
      message: `Processed ${processedCount} pending uploads`,
      data: {
        processedCount
      }
    });
  } catch (error) {
    logger.error('Process pending uploads error:', error);
    throw error;
  }
}));

/**
 * Ingest text content directly
 * POST /v1/ingest/text
 */
router.post('/text', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { text, filename, metadata } = req.body;

    if (!text || typeof text !== 'string') {
      res.status(400).json({
        status: 'error',
        message: 'Text content is required',
        code: 'INVALID_INPUT'
      });
      return;
    }

    // Create a temporary text file for processing
    const fs = require('fs');
    const path = require('path');
    const tempDir = path.join(process.cwd(), 'temp');
    const tempFilename = `text_${Date.now()}.txt`;
    const tempPath = path.join(tempDir, tempFilename);

    // Ensure temp directory exists
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write text to temporary file
    fs.writeFileSync(tempPath, text, 'utf-8');

    try {
      // Create upload record
      const upload = await uploadService.createUpload({
        filename: tempFilename,
        originalName: filename || 'text_content.txt',
        mimeType: 'text/plain',
        size: Buffer.byteLength(text, 'utf-8'),
        path: tempPath,
        userId: req.user?.id
      });

      // Process the text
      const options: ProcessingOptions = {
        preserveFormatting: req.body.preserveFormatting || true,
        includeMetadata: req.body.includeMetadata || true,
        chunkSize: req.body.chunkSize || 1000,
        chunkOverlap: req.body.chunkOverlap || 200
      };

      const result = await documentProcessingService.processUploadedDocument(upload._id.toString(), options);

      // Clean up temporary file
      fs.unlinkSync(tempPath);

      res.json({
        status: 'success',
        message: 'Text content processed successfully',
        data: {
          uploadId: upload._id,
          success: result.success,
          text: result.text,
          metadata: result.metadata,
          chunks: result.chunks,
          processingTime: result.processingTime,
          error: result.error
        }
      });
    } catch (error) {
      // Clean up temporary file on error
      if (fs.existsSync(tempPath)) {
        fs.unlinkSync(tempPath);
      }
      throw error;
    }
  } catch (error) {
    logger.error('Text ingestion error:', error);
    throw error;
  }
}));

/**
 * Get upload status
 * GET /v1/ingest/status/:uploadId
 */
router.get('/status/:uploadId', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { uploadId } = req.params;
    const progress = await uploadService.getProgress(uploadId);

    res.json({
      status: 'success',
      data: progress
    });
  } catch (error) {
    logger.error('Get upload status error:', error);
    throw error;
  }
}));

/**
 * Get user uploads
 * GET /v1/ingest/uploads
 */
router.get('/uploads', asyncHandler(async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const offset = parseInt(req.query.offset as string) || 0;

    // For now, get all uploads (will filter by user when auth is implemented)
    const uploads = await uploadService.getAllUploads(limit, offset);

    res.json({
      status: 'success',
      data: uploads.map(upload => ({
        uploadId: upload._id,
        filename: upload.originalName,
        size: upload.size,
        mimeType: upload.mimeType,
        status: upload.status,
        processingStatus: upload.processingStatus,
        uploadProgress: upload.uploadProgress,
        processingProgress: upload.processingProgress,
        error: upload.error?.message,
        createdAt: upload.createdAt,
        completedAt: upload.completedAt
      })),
      pagination: {
        limit,
        offset,
        total: uploads.length
      }
    });
  } catch (error) {
    logger.error('Get uploads error:', error);
    throw error;
  }
}));

/**
 * Delete upload
 * DELETE /v1/ingest/uploads/:uploadId
 */
router.delete('/uploads/:uploadId', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { uploadId } = req.params;
    await uploadService.deleteUpload(uploadId);

    res.json({
      status: 'success',
      message: 'Upload deleted successfully'
    });
  } catch (error) {
    logger.error('Delete upload error:', error);
    throw error;
  }
}));

/**
 * Reprocess a failed upload
 * POST /v1/ingest/reprocess/:uploadId
 */
router.post('/reprocess/:uploadId', asyncHandler(async (req: Request, res: Response) => {
  try {
    const { uploadId } = req.params;
    const options: ProcessingOptions = {
      extractImages: req.body.extractImages || false,
      preserveFormatting: req.body.preserveFormatting || true,
      includeMetadata: req.body.includeMetadata || true,
      chunkSize: req.body.chunkSize || 1000,
      chunkOverlap: req.body.chunkOverlap || 200,
      ocrLanguage: req.body.ocrLanguage || 'eng',
      csvHasHeaders: req.body.csvHasHeaders !== false,
      timeout: req.body.timeout || 300000
    };

    const result = await documentProcessingService.reprocessUpload(uploadId, options);

    res.json({
      status: 'success',
      message: 'Document reprocessed successfully',
      data: {
        uploadId,
        success: result.success,
        text: result.text,
        metadata: result.metadata,
        chunks: result.chunks,
        processingTime: result.processingTime,
        error: result.error
      }
    });
  } catch (error) {
    logger.error('Document reprocessing error:', error);
    throw error;
  }
}));

/**
 * Check if file type is supported
 * GET /v1/ingest/supported-types
 */
router.get('/supported-types', asyncHandler(async (req: Request, res: Response) => {
  try {
    const supportedExtensions = documentProcessingService.getSupportedExtensions();
    const config = documentProcessingService.getProcessorConfig();

    res.json({
      status: 'success',
      data: {
        supportedExtensions,
        supportedTypes: config.supportedTypes,
        maxFileSize: config.maxFileSize,
        ocrEnabled: config.ocrEnabled
      }
    });
  } catch (error) {
    logger.error('Get supported types error:', error);
    throw error;
  }
}));

/**
 * Get processing statistics (admin endpoint)
 * GET /v1/ingest/processing-stats
 */
router.get('/processing-stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const stats = await documentProcessingService.getProcessingStats();

    res.json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    logger.error('Get processing stats error:', error);
    throw error;
  }
}));

/**
 * Get upload statistics (admin endpoint)
 * GET /v1/ingest/stats
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const stats = await uploadService.getUploadStats();

    res.json({
      status: 'success',
      data: stats
    });
  } catch (error) {
    logger.error('Get upload stats error:', error);
    throw error;
  }
}));

/**
 * Manual cleanup (admin endpoint)
 * POST /v1/ingest/cleanup
 */
router.post('/cleanup', asyncHandler(async (req: Request, res: Response) => {
  try {
    const result = await fileCleanupService.runManualCleanup();

    res.json({
      status: 'success',
      message: 'Cleanup completed successfully',
      data: result
    });
  } catch (error) {
    logger.error('Manual cleanup error:', error);
    throw error;
  }
}));

/**
 * Get disk usage (admin endpoint)
 * GET /v1/ingest/disk-usage
 */
router.get('/disk-usage', asyncHandler(async (req: Request, res: Response) => {
  try {
    const usage = await fileCleanupService.getDiskUsage();

    res.json({
      status: 'success',
      data: usage
    });
  } catch (error) {
    logger.error('Get disk usage error:', error);
    throw error;
  }
}));

export { router as ingestRoutes };
