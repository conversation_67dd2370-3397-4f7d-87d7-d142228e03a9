"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chunksRoutes = void 0;
const express_1 = require("express");
const router = (0, express_1.Router)();
exports.chunksRoutes = router;
router.post('/retrieve', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Chunk retrieval endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
router.post('/search', (req, res) => {
    res.status(501).json({
        status: 'error',
        message: 'Chunk retrieval endpoints not yet implemented',
        code: 'NOT_IMPLEMENTED',
    });
});
//# sourceMappingURL=chunks.js.map