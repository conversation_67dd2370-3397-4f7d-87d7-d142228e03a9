"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileCleanupService = exports.FileCleanupService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const node_cron_1 = __importDefault(require("node-cron"));
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const uploadService_1 = require("@/services/uploadService");
class FileCleanupService {
    constructor() {
        this.cleanupJob = null;
        this.uploadService = new uploadService_1.UploadService();
    }
    async cleanupTempFiles(olderThanHours = 24) {
        try {
            const uploadDir = path_1.default.join(process.cwd(), environment_1.config.upload.uploadDir);
            if (!fs_1.default.existsSync(uploadDir)) {
                logger_1.logger.debug('Upload directory does not exist, skipping cleanup');
                return 0;
            }
            const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);
            const files = fs_1.default.readdirSync(uploadDir);
            let cleanedCount = 0;
            for (const file of files) {
                try {
                    const filePath = path_1.default.join(uploadDir, file);
                    const stats = fs_1.default.statSync(filePath);
                    if (stats.isDirectory()) {
                        continue;
                    }
                    if (stats.mtime.getTime() < cutoffTime) {
                        fs_1.default.unlinkSync(filePath);
                        cleanedCount++;
                        logger_1.logger.debug(`Cleaned up temp file: ${file}`);
                    }
                }
                catch (error) {
                    logger_1.logger.error(`Failed to cleanup file ${file}:`, error);
                }
            }
            if (cleanedCount > 0) {
                logger_1.logger.info(`Cleaned up ${cleanedCount} temporary files older than ${olderThanHours} hours`);
            }
            return cleanedCount;
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup temporary files:', error);
            throw error;
        }
    }
    async cleanupOrphanedFiles() {
        try {
            const uploadDir = path_1.default.join(process.cwd(), environment_1.config.upload.uploadDir);
            if (!fs_1.default.existsSync(uploadDir)) {
                logger_1.logger.debug('Upload directory does not exist, skipping orphaned file cleanup');
                return 0;
            }
            const files = fs_1.default.readdirSync(uploadDir);
            let cleanedCount = 0;
            for (const file of files) {
                try {
                    const filePath = path_1.default.join(uploadDir, file);
                    const stats = fs_1.default.statSync(filePath);
                    if (stats.isDirectory()) {
                        continue;
                    }
                    const uploads = await this.uploadService.getAllUploads(1000, 0);
                    const hasRecord = uploads.some(upload => upload.filename === file || upload.path.endsWith(file));
                    if (!hasRecord) {
                        fs_1.default.unlinkSync(filePath);
                        cleanedCount++;
                        logger_1.logger.debug(`Cleaned up orphaned file: ${file}`);
                    }
                }
                catch (error) {
                    logger_1.logger.error(`Failed to check orphaned file ${file}:`, error);
                }
            }
            if (cleanedCount > 0) {
                logger_1.logger.info(`Cleaned up ${cleanedCount} orphaned files`);
            }
            return cleanedCount;
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup orphaned files:', error);
            throw error;
        }
    }
    async cleanupFailedUploads(olderThanDays = 7) {
        try {
            const cleanedCount = await this.uploadService.cleanupFailedUploads(olderThanDays);
            return cleanedCount;
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup failed uploads:', error);
            throw error;
        }
    }
    async getDiskUsage() {
        try {
            const uploadDir = path_1.default.join(process.cwd(), environment_1.config.upload.uploadDir);
            if (!fs_1.default.existsSync(uploadDir)) {
                return {
                    totalFiles: 0,
                    totalSize: 0,
                    totalSizeFormatted: '0 B',
                    oldestFile: null,
                    newestFile: null
                };
            }
            const files = fs_1.default.readdirSync(uploadDir);
            let totalFiles = 0;
            let totalSize = 0;
            let oldestFile = null;
            let newestFile = null;
            for (const file of files) {
                try {
                    const filePath = path_1.default.join(uploadDir, file);
                    const stats = fs_1.default.statSync(filePath);
                    if (stats.isDirectory()) {
                        continue;
                    }
                    totalFiles++;
                    totalSize += stats.size;
                    if (!oldestFile || stats.mtime < oldestFile) {
                        oldestFile = stats.mtime;
                    }
                    if (!newestFile || stats.mtime > newestFile) {
                        newestFile = stats.mtime;
                    }
                }
                catch (error) {
                    logger_1.logger.error(`Failed to get stats for file ${file}:`, error);
                }
            }
            return {
                totalFiles,
                totalSize,
                totalSizeFormatted: this.formatBytes(totalSize),
                oldestFile,
                newestFile
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get disk usage:', error);
            throw error;
        }
    }
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    startCleanupJob() {
        if (this.cleanupJob) {
            logger_1.logger.warn('Cleanup job is already running');
            return;
        }
        this.cleanupJob = node_cron_1.default.schedule('0 2 * * *', async () => {
            try {
                logger_1.logger.info('Starting scheduled file cleanup...');
                const tempCleaned = await this.cleanupTempFiles(24);
                const orphanedCleaned = await this.cleanupOrphanedFiles();
                const failedCleaned = await this.cleanupFailedUploads(7);
                logger_1.logger.info(`Scheduled cleanup completed: ${tempCleaned} temp files, ${orphanedCleaned} orphaned files, ${failedCleaned} failed uploads`);
            }
            catch (error) {
                logger_1.logger.error('Scheduled cleanup failed:', error);
            }
        }, {
            scheduled: false
        });
        this.cleanupJob.start();
        logger_1.logger.info('File cleanup job started (runs daily at 2 AM)');
    }
    stopCleanupJob() {
        if (this.cleanupJob) {
            this.cleanupJob.stop();
            this.cleanupJob = null;
            logger_1.logger.info('File cleanup job stopped');
        }
    }
    async runManualCleanup() {
        try {
            logger_1.logger.info('Starting manual file cleanup...');
            const [tempFiles, orphanedFiles, failedUploads] = await Promise.all([
                this.cleanupTempFiles(24),
                this.cleanupOrphanedFiles(),
                this.cleanupFailedUploads(7)
            ]);
            logger_1.logger.info(`Manual cleanup completed: ${tempFiles} temp files, ${orphanedFiles} orphaned files, ${failedUploads} failed uploads`);
            return {
                tempFiles,
                orphanedFiles,
                failedUploads
            };
        }
        catch (error) {
            logger_1.logger.error('Manual cleanup failed:', error);
            throw error;
        }
    }
}
exports.FileCleanupService = FileCleanupService;
exports.fileCleanupService = new FileCleanupService();
//# sourceMappingURL=fileCleanup.js.map